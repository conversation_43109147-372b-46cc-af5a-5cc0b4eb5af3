import google.generativeai as genai
from config.settings import settings
from models.quiz import Question, QuestionOption, QuizGenerationRequest
from typing import List
import json
import re

# Configure Gemini API
if settings.GEMINI_API_KEY:
    genai.configure(api_key=settings.GEMINI_API_KEY)

class QuizGenerator:
    def __init__(self):
        self.model = None
        if settings.GEMINI_API_KEY:
            self.model = genai.GenerativeModel('gemini-pro')
    
    async def generate_quiz(self, request: QuizGenerationRequest) -> List[Question]:
        """Generate quiz questions using Gemini API"""
        if not self.model:
            # Return mock data if no API key
            return self._generate_mock_quiz(request)
        
        try:
            prompt = self._create_quiz_prompt(request)
            response = self.model.generate_content(prompt)
            
            # Parse the response and convert to Question objects
            questions = self._parse_quiz_response(response.text)
            return questions
            
        except Exception as e:
            print(f"Error generating quiz: {e}")
            return self._generate_mock_quiz(request)
    
    def _create_quiz_prompt(self, request: QuizGenerationRequest) -> str:
        """Create prompt for Gemini API"""
        return f"""
        Generate {request.num_questions} multiple choice questions about {request.topic} 
        with {request.difficulty} difficulty level.
        
        Format the response as a JSON array where each question has:
        - "question": the question text
        - "options": array of 4 options, each with "option" text and "is_correct" boolean
        - "explanation": brief explanation of the correct answer
        
        Make sure exactly one option per question is marked as correct.
        
        Example format:
        [
            {{
                "question": "What is the capital of France?",
                "options": [
                    {{"option": "London", "is_correct": false}},
                    {{"option": "Paris", "is_correct": true}},
                    {{"option": "Berlin", "is_correct": false}},
                    {{"option": "Madrid", "is_correct": false}}
                ],
                "explanation": "Paris is the capital and largest city of France."
            }}
        ]
        """
    
    def _parse_quiz_response(self, response_text: str) -> List[Question]:
        """Parse Gemini response into Question objects"""
        try:
            # Extract JSON from response
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                questions_data = json.loads(json_str)
                
                questions = []
                for q_data in questions_data:
                    options = [
                        QuestionOption(option=opt["option"], is_correct=opt["is_correct"])
                        for opt in q_data["options"]
                    ]
                    question = Question(
                        question=q_data["question"],
                        options=options,
                        explanation=q_data.get("explanation", "")
                    )
                    questions.append(question)
                
                return questions
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            print(f"Error parsing quiz response: {e}")
            return self._generate_mock_quiz(QuizGenerationRequest(
                topic="General Knowledge",
                difficulty="medium",
                num_questions=5
            ))
    
    def _generate_mock_quiz(self, request: QuizGenerationRequest) -> List[Question]:
        """Generate mock quiz data when API is not available"""
        mock_questions = [
            Question(
                question=f"Sample question 1 about {request.topic}?",
                options=[
                    QuestionOption(option="Option A", is_correct=True),
                    QuestionOption(option="Option B", is_correct=False),
                    QuestionOption(option="Option C", is_correct=False),
                    QuestionOption(option="Option D", is_correct=False)
                ],
                explanation="This is a sample explanation for the correct answer."
            ),
            Question(
                question=f"Sample question 2 about {request.topic}?",
                options=[
                    QuestionOption(option="Option A", is_correct=False),
                    QuestionOption(option="Option B", is_correct=True),
                    QuestionOption(option="Option C", is_correct=False),
                    QuestionOption(option="Option D", is_correct=False)
                ],
                explanation="This is another sample explanation."
            )
        ]
        
        return mock_questions[:request.num_questions]

quiz_generator = QuizGenerator()
