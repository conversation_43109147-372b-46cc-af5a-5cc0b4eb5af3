#!/usr/bin/env python3
"""
LMS Setup Script
Automated setup for the Learning Management System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_python_version():
    """Check if Python version is 3.8 or higher"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_mongodb():
    """Check if MongoDB is available"""
    result = run_command("mongod --version")
    if result:
        print("✅ MongoDB is available")
        return True
    else:
        print("⚠️  MongoDB not found. Please install MongoDB or use MongoDB Atlas")
        print("   Local: https://docs.mongodb.com/manual/installation/")
        print("   Cloud: https://www.mongodb.com/cloud/atlas")
        return False

def setup_backend():
    """Setup the FastAPI backend"""
    print("\n🚀 Setting up Backend (FastAPI)...")
    
    backend_dir = Path("lms-backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    # Create virtual environment
    print("📦 Creating virtual environment...")
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        result = run_command("python -m venv venv", cwd=backend_dir)
        if not result and result is not None:
            print("❌ Failed to create virtual environment")
            return False
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        activate_script = venv_path / "Scripts" / "activate"
        pip_path = venv_path / "Scripts" / "pip"
    else:  # Unix/Linux/macOS
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
    
    # Install dependencies
    print("📦 Installing backend dependencies...")
    result = run_command(f"{pip_path} install -r requirements.txt", cwd=backend_dir)
    if result is None:
        print("❌ Failed to install backend dependencies")
        return False
    
    # Setup environment file
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("⚙️  Creating .env file...")
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("   Please edit lms-backend/.env with your configuration")
    
    print("✅ Backend setup complete")
    return True

def setup_frontend():
    """Setup the Streamlit frontend"""
    print("\n🎨 Setting up Frontend (Streamlit)...")
    
    frontend_dir = Path("streamlit-ui")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Create virtual environment
    print("📦 Creating virtual environment...")
    venv_path = frontend_dir / "venv"
    if not venv_path.exists():
        result = run_command("python -m venv venv", cwd=frontend_dir)
        if not result and result is not None:
            print("❌ Failed to create virtual environment")
            return False
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        activate_script = venv_path / "Scripts" / "activate"
        pip_path = venv_path / "Scripts" / "pip"
    else:  # Unix/Linux/macOS
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
    
    # Install dependencies
    print("📦 Installing frontend dependencies...")
    result = run_command(f"{pip_path} install -r requirements.txt", cwd=frontend_dir)
    if result is None:
        print("❌ Failed to install frontend dependencies")
        return False
    
    # Setup environment file
    env_file = frontend_dir / ".env"
    env_example = frontend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("⚙️  Creating .env file...")
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
    
    print("✅ Frontend setup complete")
    return True

def create_start_scripts():
    """Create convenient start scripts"""
    print("\n📝 Creating start scripts...")
    
    # Backend start script
    if os.name == 'nt':  # Windows
        backend_script = """@echo off
cd lms-backend
call venv\\Scripts\\activate
uvicorn main:app --reload --port 8000
"""
        with open("start_backend.bat", "w") as f:
            f.write(backend_script)
        
        frontend_script = """@echo off
cd streamlit-ui
call venv\\Scripts\\activate
streamlit run main.py --server.port 8501
"""
        with open("start_frontend.bat", "w") as f:
            f.write(frontend_script)
        
        print("✅ Created start_backend.bat and start_frontend.bat")
    
    else:  # Unix/Linux/macOS
        backend_script = """#!/bin/bash
cd lms-backend
source venv/bin/activate
uvicorn main:app --reload --port 8000
"""
        with open("start_backend.sh", "w") as f:
            f.write(backend_script)
        os.chmod("start_backend.sh", 0o755)
        
        frontend_script = """#!/bin/bash
cd streamlit-ui
source venv/bin/activate
streamlit run main.py --server.port 8501
"""
        with open("start_frontend.sh", "w") as f:
            f.write(frontend_script)
        os.chmod("start_frontend.sh", 0o755)
        
        print("✅ Created start_backend.sh and start_frontend.sh")

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup Complete!")
    print("\n📋 Next Steps:")
    print("1. Configure your environment variables:")
    print("   - Edit lms-backend/.env with your MongoDB URL and API keys")
    print("   - Edit streamlit-ui/.env if needed")
    print("\n2. Start MongoDB (if using local installation)")
    print("\n3. Start the applications:")
    
    if os.name == 'nt':  # Windows
        print("   Backend:  start_backend.bat")
        print("   Frontend: start_frontend.bat")
    else:  # Unix/Linux/macOS
        print("   Backend:  ./start_backend.sh")
        print("   Frontend: ./start_frontend.sh")
    
    print("\n4. Access the application:")
    print("   Frontend: http://localhost:8501")
    print("   Backend API: http://localhost:8000")
    print("   API Docs: http://localhost:8000/docs")
    
    print("\n5. Create admin account:")
    print("   Use the 'Setup Admin Account' button on the homepage")
    
    print("\n📚 Documentation:")
    print("   - Main README.md")
    print("   - lms-backend/README.md")
    print("   - streamlit-ui/README.md")

def main():
    """Main setup function"""
    print("🎓 Learning Management System Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    check_mongodb()  # Warning only, not required for setup
    
    # Setup components
    backend_success = setup_backend()
    frontend_success = setup_frontend()
    
    if backend_success and frontend_success:
        create_start_scripts()
        print_next_steps()
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
