import streamlit as st
from utils.auth import is_authenticated, get_user_role, logout

# Page configuration
st.set_page_config(
    page_title="Learning Management System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .role-badge {
        background-color: #f0f2f6;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        color: #262730;
    }
    
    .stats-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 1px solid #e1e5e9;
    }
    
    .feature-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Initialize page state
    if "page" not in st.session_state:
        st.session_state.page = "home"

    # Debug information (remove in production)
    with st.sidebar:
        st.markdown("### 🔧 Debug Info")
        st.write(f"**Authenticated:** {is_authenticated()}")
        st.write(f"**JWT Token:** {'Present' if 'jwt_token' in st.session_state else 'Missing'}")
        st.write(f"**User Data:** {'Present' if 'user' in st.session_state else 'Missing'}")
        st.write(f"**Page:** {st.session_state.get('page', 'None')}")
        if is_authenticated():
            st.write(f"**User Role:** {get_user_role()}")
            st.write(f"**User:** {st.session_state.get('user', {}).get('full_name', 'Unknown')}")

        # Manual clear button for testing
        if st.button("🗑️ Clear Session"):
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()

    # MAIN ROUTING LOGIC
    authenticated = is_authenticated()

    if authenticated:
        # User is logged in - show role-based dashboard
        user_role = get_user_role()
        st.markdown(f"### ✅ Authenticated as {user_role.upper()}")
        show_role_dashboard(user_role)
    else:
        # User not logged in - show authentication flow
        st.markdown("### ❌ Not Authenticated - Showing Auth Flow")
        show_auth_flow()

def show_auth_flow():
    """Show authentication flow based on current page state"""
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎓 Learning Management System</h1>
        <p>AI-Powered Education Platform</p>
    </div>
    """, unsafe_allow_html=True)

    # Page routing for authentication
    if st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "register":
        show_register_page()
    else:
        show_welcome_page()

def show_welcome_page():
    """Show welcome page for non-authenticated users"""
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("### Welcome to the LMS Platform")
        st.markdown("""
        Our Learning Management System offers:

        🤖 **AI-Powered Features**
        - Automated quiz generation using Gemini AI
        - Intelligent essay grading
        - Personalized course recommendations
        - Performance prediction using machine learning

        👥 **Role-Based Access**
        - **Students**: Take courses, quizzes, and track progress
        - **Professors**: Create courses, generate quizzes, grade essays
        - **Administrators**: Manage users and analyze performance

        📊 **Advanced Analytics**
        - Performance summaries and insights
        - Progress tracking and recommendations
        - Comprehensive reporting tools
        """)

        st.markdown("---")

        # Login/Register buttons
        col_login, col_register = st.columns(2)

        with col_login:
            if st.button("🔑 Login", use_container_width=True, type="primary"):
                st.session_state.page = "login"
                st.rerun()

        with col_register:
            if st.button("📝 Register", use_container_width=True):
                st.session_state.page = "register"
                st.rerun()

        # Demo credentials
        with st.expander("🔧 Demo Credentials"):
            st.markdown("""
            **Admin Account:**
            - Email: <EMAIL>
            - Password: admin123

            **Professor Account:**
            - Email: <EMAIL>
            - Password: prof123

            **Student Account:**
            - Email: <EMAIL>
            - Password: student123

            **Note:** Create these accounts through registration if they don't exist.
            """)

            col_setup1, col_setup2 = st.columns(2)

            with col_setup1:
                if st.button("🛠️ Setup Admin Account", use_container_width=True):
                    try:
                        from services.api import api_client
                        result = api_client.create_admin()
                        st.success("✅ Admin account created successfully!")
                        st.json(result)
                    except Exception as e:
                        if "already exists" in str(e):
                            st.info("ℹ️ Admin account already exists")
                        else:
                            st.error(f"❌ Error creating admin account: {e}")

            with col_setup2:
                if st.button("🛠️ Create Demo Accounts", use_container_width=True):
                    try:
                        from services.api import api_client

                        # Create professor account
                        try:
                            prof_data = {
                                "full_name": "Demo Professor",
                                "email": "<EMAIL>",
                                "password": "prof123",
                                "role": "professor"
                            }
                            api_client.register(prof_data)
                            st.success("✅ Professor account created!")
                        except Exception as e:
                            if "already exists" in str(e):
                                st.info("ℹ️ Professor account already exists")

                        # Create student account
                        try:
                            student_data = {
                                "full_name": "Demo Student",
                                "email": "<EMAIL>",
                                "password": "student123",
                                "role": "student"
                            }
                            api_client.register(student_data)
                            st.success("✅ Student account created!")
                        except Exception as e:
                            if "already exists" in str(e):
                                st.info("ℹ️ Student account already exists")

                    except Exception as e:
                        st.error(f"❌ Error creating demo accounts: {e}")

def show_login_page():
    """Show login page"""
    from services.api import api_client

    # Clean header for login page
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white; margin-bottom: 2rem;">
        <h1>🔑 Login to LMS</h1>
        <p>Enter your credentials to access your dashboard</p>
    </div>
    """, unsafe_allow_html=True)

    # Center the login form
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        with st.form("login_form"):
            email = st.text_input("📧 Email Address", placeholder="Enter your email")
            password = st.text_input("🔒 Password", type="password", placeholder="Enter your password")

            # Login button
            login_button = st.form_submit_button("🔑 Login", use_container_width=True, type="primary")

            # Navigation buttons
            col_reg, col_home = st.columns(2)
            with col_reg:
                if st.form_submit_button("📝 Register Instead", use_container_width=True):
                    st.session_state.page = "register"
                    st.rerun()

            with col_home:
                if st.form_submit_button("🏠 Back to Home", use_container_width=True):
                    st.session_state.page = "home"
                    st.rerun()

        # Handle login
        if login_button:
            if not email or not password:
                st.error("Please enter both email and password")
            else:
                try:
                    with st.spinner("Logging in..."):
                        response = api_client.login(email, password)

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Show success message
                    st.success(f"✅ Welcome back, {response['user']['full_name']}!")
                    st.balloons()

                    # Small delay to show success message
                    import time
                    time.sleep(1)

                    # Clear page state to trigger dashboard routing
                    if "page" in st.session_state:
                        del st.session_state.page

                    # Redirect to role-based dashboard
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Login failed: {str(e)}")

        # Quick demo login buttons
        st.markdown("---")
        st.markdown("### 🚀 Quick Demo Login")

        col_admin, col_prof, col_student = st.columns(3)

        with col_admin:
            if st.button("👑 Login as Admin", use_container_width=True, key="quick_admin_login"):
                try:
                    with st.spinner("Logging in as Admin..."):
                        response = api_client.login("<EMAIL>", "admin123")

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Clear page state to trigger dashboard routing
                    if "page" in st.session_state:
                        del st.session_state.page

                    st.success("✅ Logged in as Admin!")

                    # Force immediate rerun
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Admin login failed: {str(e)}")
                    st.info("💡 Try creating the admin account first using the setup button above.")

        with col_prof:
            if st.button("👨‍🏫 Login as Professor", use_container_width=True, key="quick_prof_login"):
                try:
                    with st.spinner("Logging in as Professor..."):
                        response = api_client.login("<EMAIL>", "prof123")

                    # Debug: Show response
                    st.write("Debug - Login Response:", response)

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Debug: Show stored data
                    st.write("Debug - Stored JWT:", st.session_state.jwt_token[:20] + "...")
                    st.write("Debug - Stored User:", st.session_state.user)

                    # Clear page state to trigger dashboard routing
                    if "page" in st.session_state:
                        del st.session_state.page

                    st.success("✅ Logged in as Professor!")
                    st.info("🔄 Reloading page to show dashboard...")

                    # Add a small delay before rerun
                    import time
                    time.sleep(1)

                    # Force immediate rerun
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Professor login failed: {str(e)}")
                    st.info("💡 Please register a professor account first.")

        with col_student:
            if st.button("👨‍🎓 Login as Student", use_container_width=True, key="quick_student_login"):
                try:
                    with st.spinner("Logging in as Student..."):
                        response = api_client.login("<EMAIL>", "student123")

                    # Debug: Show response
                    st.write("Debug - Login Response:", response)

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Debug: Show stored data
                    st.write("Debug - Stored JWT:", st.session_state.jwt_token[:20] + "...")
                    st.write("Debug - Stored User:", st.session_state.user)

                    # Clear page state to trigger dashboard routing
                    if "page" in st.session_state:
                        del st.session_state.page

                    st.success("✅ Logged in as Student!")
                    st.info("🔄 Reloading page to show dashboard...")

                    # Add a small delay before rerun
                    import time
                    time.sleep(1)

                    # Force immediate rerun
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Student login failed: {str(e)}")
                    st.info("💡 Please register a student account first.")

def show_register_page():
    """Show registration page"""
    from services.api import api_client

    # Clean header for register page
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(90deg, #28a745 0%, #20c997 100%); border-radius: 10px; color: white; margin-bottom: 2rem;">
        <h1>📝 Register for LMS</h1>
        <p>Create your account to start learning</p>
    </div>
    """, unsafe_allow_html=True)

    # Center the registration form
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        with st.form("register_form"):
            full_name = st.text_input("👤 Full Name", placeholder="Enter your full name")
            email = st.text_input("📧 Email Address", placeholder="Enter your email")
            password = st.text_input("🔒 Password", type="password", placeholder="Create a password")
            confirm_password = st.text_input("🔒 Confirm Password", type="password", placeholder="Confirm your password")

            role = st.selectbox("👥 Select your role", options=["student", "professor"], help="Choose your role in the system")

            # Role information
            if role == "student":
                st.info("👨‍🎓 **Student Role:** Enroll in courses, take quizzes, track progress, get recommendations")
            elif role == "professor":
                st.info("👨‍🏫 **Professor Role:** Create courses, generate AI quizzes, grade essays, predict performance")

            terms_accepted = st.checkbox("✅ I agree to the Terms of Service and Privacy Policy")

            # Register button
            register_button = st.form_submit_button("📝 Create Account", use_container_width=True, type="primary")

            # Navigation buttons
            col_login, col_home = st.columns(2)
            with col_login:
                if st.form_submit_button("🔑 Login Instead", use_container_width=True):
                    st.session_state.page = "login"
                    st.rerun()

            with col_home:
                if st.form_submit_button("🏠 Back to Home", use_container_width=True):
                    st.session_state.page = "home"
                    st.rerun()

        # Handle registration
        if register_button:
            errors = []

            if not full_name:
                errors.append("Full name is required")
            if not email or "@" not in email:
                errors.append("Valid email is required")
            if not password or len(password) < 6:
                errors.append("Password must be at least 6 characters")
            if password != confirm_password:
                errors.append("Passwords do not match")
            if not terms_accepted:
                errors.append("You must accept the terms")

            if errors:
                for error in errors:
                    st.error(f"❌ {error}")
            else:
                try:
                    with st.spinner("Creating your account..."):
                        user_data = {
                            "full_name": full_name,
                            "email": email,
                            "password": password,
                            "role": role
                        }
                        response = api_client.register(user_data)

                    # Store authentication data
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]

                    # Show success message
                    st.success(f"✅ Account created! Welcome, {full_name}!")
                    st.balloons()

                    # Small delay to show success message
                    import time
                    time.sleep(1)

                    # Clear page state to trigger dashboard routing
                    if "page" in st.session_state:
                        del st.session_state.page

                    # Redirect to role-based dashboard
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Registration failed: {str(e)}")

def show_role_dashboard(user_role):
    """Show role-based dashboard without sidebar"""
    user = st.session_state.get("user", {})

    # Top navigation bar with logout
    col1, col2, col3 = st.columns([3, 1, 1])

    with col1:
        st.markdown(f"""
        <div style="padding: 1rem; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white;">
            <h2>Welcome, {user.get('full_name', 'User')}!</h2>
            <p>Role: {user_role.upper()} | Dashboard</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        if st.button("🏠 Home", use_container_width=True):
            st.session_state.page = "home"
            st.rerun()

    with col3:
        if st.button("🚪 Logout", use_container_width=True, type="secondary"):
            logout()

    st.markdown("---")

    # Route to specific dashboard based on role
    if user_role == "admin":
        show_admin_dashboard_content()
    elif user_role == "professor":
        show_professor_dashboard_content()
    elif user_role == "student":
        show_student_dashboard_content()

def show_admin_dashboard_content():
    """Show admin dashboard content"""
    from services.api import api_client

    st.markdown("## 👑 Admin Dashboard")

    # Quick stats
    try:
        with st.spinner("Loading dashboard data..."):
            stats = api_client.get_admin_dashboard_stats()

        # Key metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_users = sum(stats["user_statistics"].values())
            st.metric("Total Users", total_users)

        with col2:
            total_courses = stats["course_statistics"]["total_courses"]
            st.metric("Total Courses", total_courses)

        with col3:
            total_attempts = stats["quiz_statistics"]["total_attempts"]
            st.metric("Quiz Attempts", total_attempts)

        with col4:
            new_users = stats["recent_activity"]["new_users_this_week"]
            st.metric("New Users (7d)", new_users)

        st.markdown("---")

        # Quick actions
        st.markdown("### ⚡ Quick Actions")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("👥 Manage Users", use_container_width=True, type="primary"):
                st.info("User management functionality - Create, edit, delete users")

        with col2:
            if st.button("📊 View All Courses", use_container_width=True, type="primary"):
                try:
                    courses = api_client.get_all_courses()
                    st.success(f"Found {len(courses)} courses in the system")
                    for course in courses[:5]:  # Show first 5
                        st.write(f"📚 {course['title']} - {course['professor_name']}")
                except Exception as e:
                    st.error(f"Error loading courses: {e}")

        with col3:
            if st.button("📈 Performance Analysis", use_container_width=True, type="primary"):
                st.info("Upload student performance data and generate AI insights")

    except Exception as e:
        st.error(f"Error loading admin dashboard: {e}")

def show_professor_dashboard_content():
    """Show comprehensive professor dashboard content"""
    from services.api import api_client
    import plotly.express as px
    import plotly.graph_objects as go

    st.markdown("## 👨‍🏫 Professor Dashboard")

    # Quick stats
    try:
        with st.spinner("Loading your dashboard..."):
            stats = api_client.get_professor_dashboard_stats()

        # Key metrics with enhanced styling
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_courses = stats["course_statistics"]["total_courses"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{total_courses}</h2>
                <p style="margin: 0; opacity: 0.9;">My Courses</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            total_students = stats["course_statistics"]["total_enrolled_students"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{total_students}</h2>
                <p style="margin: 0; opacity: 0.9;">Total Students</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            total_quizzes = stats["quiz_statistics"]["total_quizzes"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{total_quizzes}</h2>
                <p style="margin: 0; opacity: 0.9;">Quizzes Created</p>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            total_attempts = stats["quiz_statistics"]["total_attempts"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{total_attempts}</h2>
                <p style="margin: 0; opacity: 0.9;">Quiz Attempts</p>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("---")

        # AI Tools Section
        st.markdown("### 🤖 AI-Powered Teaching Tools")

        # Quiz Generator
        with st.expander("🧠 AI Quiz Generator", expanded=False):
            st.markdown("Generate quiz questions using AI based on topics and difficulty levels.")

            col1, col2 = st.columns(2)
            with col1:
                topic = st.text_input("Enter topic:", placeholder="e.g., Python Programming", key="quiz_topic")
                difficulty = st.selectbox("Difficulty:", ["easy", "medium", "hard"], key="quiz_difficulty")
                num_questions = st.slider("Number of questions:", 1, 10, 5, key="quiz_num")

            with col2:
                if st.button("🧠 Generate Quiz", use_container_width=True, type="primary", key="generate_quiz"):
                    if topic:
                        try:
                            with st.spinner("Generating quiz with AI..."):
                                result = api_client.generate_quiz({
                                    "topic": topic,
                                    "difficulty": difficulty,
                                    "num_questions": num_questions
                                })

                            st.success("✅ Quiz generated successfully!")

                            for i, question in enumerate(result["questions"], 1):
                                st.markdown(f"**Question {i}:** {question['question']}")
                                for j, option in enumerate(question['options']):
                                    prefix = "✅" if option['is_correct'] else "❌"
                                    st.markdown(f"{prefix} {option['option']}")
                                if question.get('explanation'):
                                    st.markdown(f"*💡 Explanation: {question['explanation']}*")
                                st.markdown("---")

                        except Exception as e:
                            st.error(f"Error generating quiz: {e}")
                    else:
                        st.error("Please enter a topic")

        # Essay Grader
        with st.expander("📝 AI Essay Grader", expanded=False):
            st.markdown("Automatically grade essays with detailed feedback using AI.")

            essay_text = st.text_area(
                "Essay to grade:",
                placeholder="Paste the student's essay here...",
                height=150,
                key="essay_text"
            )

            col1, col2 = st.columns(2)
            with col1:
                rubric = st.text_area(
                    "Grading rubric:",
                    value="Grade based on content quality, organization, grammar, and creativity. Focus on clarity of arguments and use of evidence.",
                    height=100,
                    key="essay_rubric"
                )

            with col2:
                max_score = st.number_input("Maximum score:", min_value=1, max_value=100, value=100, key="essay_max_score")

                if st.button("📝 Grade Essay", use_container_width=True, type="primary", key="grade_essay"):
                    if essay_text:
                        try:
                            with st.spinner("Grading essay with AI..."):
                                result = api_client.grade_essay({
                                    "essay_text": essay_text,
                                    "rubric": rubric,
                                    "max_score": max_score
                                })

                            grading = result["grading_result"]

                            # Display results
                            col_score, col_percent = st.columns(2)
                            with col_score:
                                st.metric("Score", f"{grading['overall_score']}/{grading['max_score']}")
                            with col_percent:
                                st.metric("Percentage", f"{grading['percentage']:.1f}%")

                            col_strengths, col_improvements = st.columns(2)

                            with col_strengths:
                                st.markdown("**✅ Strengths:**")
                                for strength in grading['strengths']:
                                    st.markdown(f"• {strength}")

                            with col_improvements:
                                st.markdown("**📈 Areas for Improvement:**")
                                for improvement in grading['areas_for_improvement']:
                                    st.markdown(f"• {improvement}")

                            st.markdown("**📝 Detailed Feedback:**")
                            st.info(grading['detailed_feedback'])

                        except Exception as e:
                            st.error(f"Error grading essay: {e}")
                    else:
                        st.error("Please enter an essay to grade")

        # Score Predictor
        with st.expander("🔮 ML Score Predictor", expanded=False):
            st.markdown("Predict student performance using machine learning models.")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Student Performance Data:**")
                prev_scores = st.slider("Previous quiz scores (avg):", 0, 100, 75, key="pred_prev_scores")
                study_hours = st.slider("Study hours per week:", 0, 40, 10, key="pred_study_hours")
                attendance = st.slider("Attendance rate (%):", 0, 100, 85, key="pred_attendance")

            with col2:
                assignment_completion = st.slider("Assignment completion (%):", 0, 100, 90, key="pred_assignments")
                participation = st.slider("Participation score:", 0, 100, 80, key="pred_participation")
                course_difficulty = st.selectbox("Course difficulty:", [1, 2, 3, 4, 5], index=2, key="pred_difficulty")

                if st.button("🔮 Predict Score", use_container_width=True, type="primary", key="predict_score"):
                    try:
                        with st.spinner("Predicting score with ML..."):
                            student_data = {
                                "previous_quiz_scores": prev_scores,
                                "study_hours": study_hours,
                                "attendance_rate": attendance,
                                "assignment_completion": assignment_completion,
                                "participation_score": participation,
                                "course_difficulty": course_difficulty
                            }
                            result = api_client.predict_score(student_data)

                        prediction = result["prediction"]

                        st.success(f"🎯 Predicted Score: {prediction['predicted_score']:.1f}%")
                        st.info(f"Confidence: {prediction['confidence'].title()}")

                        # Feature importance chart
                        if 'feature_importance' in prediction:
                            importance_data = prediction['feature_importance']
                            fig = px.bar(
                                x=list(importance_data.values()),
                                y=list(importance_data.keys()),
                                orientation='h',
                                title="Feature Importance in Prediction"
                            )
                            fig.update_layout(height=300)
                            st.plotly_chart(fig, use_container_width=True)

                    except Exception as e:
                        st.error(f"Error predicting score: {e}")

        st.markdown("---")

        # Course Management Section
        st.markdown("### 📚 Course Management")

        # Create New Course
        with st.expander("➕ Create New Course", expanded=False):
            with st.form("create_course_form"):
                course_title = st.text_input("Course Title:", placeholder="e.g., Introduction to Python Programming")
                course_description = st.text_area("Course Description:", placeholder="Describe what students will learn...")
                course_category = st.selectbox("Category:", ["Programming", "Mathematics", "Science", "Business", "Arts", "Other"])
                course_difficulty = st.selectbox("Difficulty Level:", ["beginner", "intermediate", "advanced"])

                if st.form_submit_button("➕ Create Course", use_container_width=True, type="primary"):
                    if course_title and course_description:
                        try:
                            with st.spinner("Creating course..."):
                                course_data = {
                                    "title": course_title,
                                    "description": course_description,
                                    "category": course_category,
                                    "difficulty_level": course_difficulty
                                }
                                result = api_client.create_course(course_data)

                            st.success(f"✅ Course '{course_title}' created successfully!")
                            st.rerun()

                        except Exception as e:
                            st.error(f"Error creating course: {e}")
                    else:
                        st.error("Please fill in all required fields")

        # My Courses
        st.markdown("### 📋 My Courses")

        try:
            courses = api_client.get_professor_courses()
            if courses:
                for course in courses:
                    with st.container():
                        st.markdown(f"""
                        <div style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 1rem; border-left: 4px solid #6f42c1;">
                            <h4>{course['title']}</h4>
                            <p><strong>Category:</strong> {course['category']} | <strong>Difficulty:</strong> {course['difficulty_level'].title()}</p>
                            <p><strong>Enrolled Students:</strong> {course['enrolled_count']}</p>
                            <p>{course['description'][:150]}...</p>
                        </div>
                        """, unsafe_allow_html=True)

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            if st.button(f"➕ Add Quiz", key=f"add_quiz_{course['id']}", use_container_width=True):
                                st.info("Quiz creation functionality - coming soon!")

                        with col2:
                            if st.button(f"📊 View Results", key=f"results_{course['id']}", use_container_width=True):
                                st.info("Quiz results viewing - coming soon!")

                        with col3:
                            if st.button(f"✏️ Edit Course", key=f"edit_{course['id']}", use_container_width=True):
                                st.info("Course editing - coming soon!")
            else:
                st.info("🎯 You haven't created any courses yet. Use the 'Create New Course' section above to get started!")
        except Exception as e:
            st.error(f"Error loading courses: {e}")

    except Exception as e:
        st.error(f"Error loading professor dashboard: {e}")

def show_student_dashboard_content():
    """Show comprehensive student dashboard content"""
    from services.api import api_client
    import plotly.express as px
    import plotly.graph_objects as go

    st.markdown("## 👨‍🎓 Student Dashboard")

    # Quick stats
    try:
        with st.spinner("Loading your dashboard..."):
            stats = api_client.get_student_dashboard_stats()

        # Key metrics with enhanced styling
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            enrolled_count = stats["enrollment_statistics"]["enrolled_courses"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{enrolled_count}</h2>
                <p style="margin: 0; opacity: 0.9;">Enrolled Courses</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            total_quizzes = stats["quiz_statistics"]["total_quizzes_taken"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{total_quizzes}</h2>
                <p style="margin: 0; opacity: 0.9;">Quizzes Taken</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            avg_score = stats["quiz_statistics"]["average_score"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{avg_score:.1f}%</h2>
                <p style="margin: 0; opacity: 0.9;">Average Score</p>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            highest_score = stats["quiz_statistics"]["highest_score"]
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 1.5rem; border-radius: 10px; color: white; text-align: center;">
                <h2 style="margin: 0; font-size: 2.5rem;">{highest_score:.1f}%</h2>
                <p style="margin: 0; opacity: 0.9;">Best Score</p>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("---")

        # Performance Chart
        if stats["quiz_statistics"]["recent_scores"]:
            st.markdown("### 📈 Performance Trend")

            recent_scores = stats["quiz_statistics"]["recent_scores"]

            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=list(range(1, len(recent_scores) + 1)),
                y=recent_scores,
                mode='lines+markers',
                name='Quiz Scores',
                line=dict(color='#28a745', width=3),
                marker=dict(size=8, color='#28a745')
            ))

            fig.update_layout(
                title="Recent Quiz Performance",
                xaxis_title="Quiz Number",
                yaxis_title="Score (%)",
                height=400,
                showlegend=False,
                yaxis=dict(range=[0, 100]),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )

            st.plotly_chart(fig, use_container_width=True)

        st.markdown("---")

        # Course Browsing and Enrollment
        st.markdown("### 🔍 Browse & Enroll in Courses")

        with st.expander("🔍 Available Courses", expanded=False):
            try:
                courses = api_client.get_available_courses()
                if courses:
                    st.success(f"Found {len(courses)} available courses")

                    # Filter options
                    col1, col2 = st.columns(2)
                    with col1:
                        categories = list(set([course.get('category', 'Other') for course in courses]))
                        selected_category = st.selectbox("Filter by category:", ["All"] + categories, key="course_filter")

                    with col2:
                        difficulties = list(set([course.get('difficulty_level', 'beginner') for course in courses]))
                        selected_difficulty = st.selectbox("Filter by difficulty:", ["All"] + difficulties, key="difficulty_filter")

                    # Filter courses
                    filtered_courses = courses
                    if selected_category != "All":
                        filtered_courses = [c for c in filtered_courses if c.get('category') == selected_category]
                    if selected_difficulty != "All":
                        filtered_courses = [c for c in filtered_courses if c.get('difficulty_level') == selected_difficulty]

                    # Display courses
                    for course in filtered_courses[:10]:  # Show first 10
                        st.markdown(f"""
                        <div style="background: white; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border-left: 4px solid #28a745;">
                            <h4>{course['title']}</h4>
                            <p><strong>Professor:</strong> {course['professor_name']} | <strong>Category:</strong> {course['category']} | <strong>Difficulty:</strong> {course['difficulty_level'].title()}</p>
                            <p>{course['description'][:200]}...</p>
                            <p><strong>Enrolled Students:</strong> {course['enrolled_count']}</p>
                        </div>
                        """, unsafe_allow_html=True)

                        if st.button(f"� Enroll in Course", key=f"enroll_{course['id']}", use_container_width=True):
                            try:
                                with st.spinner("Enrolling..."):
                                    result = api_client.enroll_in_course(course['id'])
                                st.success(f"✅ Successfully enrolled in '{course['title']}'!")
                                st.rerun()
                            except Exception as e:
                                if "already enrolled" in str(e).lower():
                                    st.info("You're already enrolled in this course!")
                                else:
                                    st.error(f"Error enrolling: {e}")
                else:
                    st.info("No courses available at the moment.")
            except Exception as e:
                st.error(f"Error loading courses: {e}")

        # Course Recommendations
        st.markdown("### 💡 Personalized Recommendations")

        with st.expander("💡 Course Recommendations", expanded=False):
            try:
                recommendations = api_client.get_course_recommendations()
                recs = recommendations.get("recommendations", [])
                if recs:
                    st.success(f"Found {len(recs)} personalized recommendations for you!")

                    for rec in recs:
                        st.markdown(f"""
                        <div style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); padding: 1rem; border-radius: 8px; margin-bottom: 1rem; color: white;">
                            <h4>{rec['title']}</h4>
                            <p><strong>Category:</strong> {rec['category']} | <strong>Difficulty:</strong> {rec['difficulty_level'].title()}</p>
                            <p>{rec['description'][:150]}...</p>
                            <p><strong>💡 Why recommended:</strong> {rec['recommendation_reason']}</p>
                            <p><strong>Match Score:</strong> {rec.get('similarity_score', 0):.2f}</p>
                        </div>
                        """, unsafe_allow_html=True)

                        if st.button(f"📚 Enroll Now", key=f"rec_enroll_{rec['course_id']}", use_container_width=True):
                            try:
                                with st.spinner("Enrolling..."):
                                    result = api_client.enroll_in_course(rec['course_id'])
                                st.success(f"✅ Successfully enrolled in '{rec['title']}'!")
                                st.rerun()
                            except Exception as e:
                                if "already enrolled" in str(e).lower():
                                    st.info("You're already enrolled in this course!")
                                else:
                                    st.error(f"Error enrolling: {e}")
                else:
                    st.info("💡 No recommendations available yet. Enroll in more courses to get personalized suggestions!")
            except Exception as e:
                st.error(f"Error getting recommendations: {e}")

        # My Enrolled Courses
        st.markdown("### 📚 My Enrolled Courses")

        try:
            enrolled_courses = api_client.get_enrolled_courses()
            if enrolled_courses:
                for course in enrolled_courses:
                    st.markdown(f"""
                    <div style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 1rem; border-left: 4px solid #28a745;">
                        <h4>{course['title']}</h4>
                        <p><strong>Professor:</strong> {course['professor_name']} | <strong>Category:</strong> {course['category']} | <strong>Difficulty:</strong> {course['difficulty_level'].title()}</p>
                        <p>{course['description']}</p>
                        <div style="background-color: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden; margin: 10px 0;">
                            <div style="background: linear-gradient(90deg, #28a745 0%, #20c997 100%); height: 100%; width: 75%; border-radius: 10px;"></div>
                        </div>
                        <small>Progress: 75% Complete</small>
                    </div>
                    """, unsafe_allow_html=True)

                    col1, col2, col3 = st.columns(3)

                    with col1:
                        if st.button(f"📝 Take Quiz", key=f"quiz_{course['id']}", use_container_width=True):
                            try:
                                quizzes = api_client.get_course_quizzes(course['id'])
                                if quizzes:
                                    st.success(f"Found {len(quizzes)} quizzes for this course!")
                                    for quiz in quizzes:
                                        st.write(f"📝 {quiz['title']} - {quiz['difficulty'].title()} ({quiz['question_count']} questions)")
                                else:
                                    st.info("No quizzes available for this course yet.")
                            except Exception as e:
                                st.error(f"Error loading quizzes: {e}")

                    with col2:
                        if st.button(f"📊 View Progress", key=f"progress_{course['id']}", use_container_width=True):
                            st.info("📊 Progress tracking: 75% complete, 3 quizzes taken, average score: 85%")

                    with col3:
                        if st.button(f"📖 Course Materials", key=f"materials_{course['id']}", use_container_width=True):
                            st.info("📖 Course materials and resources - coming soon!")
            else:
                st.markdown("""
                <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; margin: 1rem 0;">
                    <h3>🎯 Ready to Start Learning?</h3>
                    <p>You haven't enrolled in any courses yet. Browse available courses above to get started!</p>
                </div>
                """, unsafe_allow_html=True)
        except Exception as e:
            st.error(f"Error loading enrolled courses: {e}")

        # Quiz Results
        st.markdown("### 📈 Recent Quiz Results")

        try:
            quiz_results = api_client.get_quiz_results()
            if quiz_results:
                recent_results = sorted(quiz_results, key=lambda x: x['submitted_at'], reverse=True)[:5]

                for result in recent_results:
                    score_color = "#28a745" if result['percentage'] >= 70 else "#dc3545"
                    st.markdown(f"""
                    <div style="background: white; padding: 1rem; border-radius: 8px; margin-bottom: 0.5rem; border-left: 4px solid {score_color};">
                        <strong>{result.get('quiz_title', 'Quiz')}</strong><br>
                        <span style="color: {score_color}; font-weight: bold; font-size: 1.2rem;">{result['percentage']:.1f}%</span>
                        <span style="color: #6c757d; margin-left: 1rem;">
                            {result['correct_answers']}/{result['total_questions']} correct
                        </span>
                        <br>
                        <small style="color: #6c757d;">
                            Submitted: {result['submitted_at'][:10]}
                        </small>
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.info("📝 No quiz results yet. Take your first quiz to see your progress here!")
        except Exception as e:
            st.error(f"Error loading quiz results: {e}")

        # Study Tips
        with st.expander("💡 Study Tips & Learning Resources", expanded=False):
            st.markdown("""
            ### 📚 Effective Study Strategies

            **🎯 Goal Setting:**
            - Set specific, measurable learning goals
            - Break large topics into smaller chunks
            - Track your progress regularly

            **⏰ Time Management:**
            - Use the Pomodoro Technique (25 min study, 5 min break)
            - Create a consistent study schedule
            - Prioritize difficult topics when you're most alert

            **🧠 Active Learning:**
            - Take notes while studying
            - Explain concepts to others
            - Practice with quizzes and exercises
            - Connect new information to what you already know

            **📊 Performance Improvement:**
            - Review quiz results and learn from mistakes
            - Focus on areas where you scored lower
            - Ask questions during lectures or in forums
            - Form study groups with classmates
            """)

    except Exception as e:
        st.error(f"Error loading student dashboard: {e}")

# Navigation functions removed - using direct dashboard content instead

if __name__ == "__main__":
    main()
