import streamlit as st
from typing import Optional

def is_authenticated() -> bool:
    """Check if user is authenticated"""
    return "jwt_token" in st.session_state and "user" in st.session_state

def get_current_user() -> Optional[dict]:
    """Get current user from session"""
    return st.session_state.get("user")

def get_user_role() -> Optional[str]:
    """Get current user's role"""
    user = get_current_user()
    return user.get("role") if user else None

def require_auth():
    """Require authentication - redirect to login if not authenticated"""
    if not is_authenticated():
        st.error("Please login to access this page")
        st.info("👆 Click the Login button below to continue")
        if st.button("🔑 Go to Login", type="primary"):
            st.session_state.page = "login"
            st.rerun()
        st.stop()

def require_role(required_role: str):
    """Require specific role"""
    require_auth()
    user_role = get_user_role()
    
    if user_role != required_role and user_role != "admin":
        st.error(f"Access denied. {required_role.title()} role required.")
        st.stop()

def logout():
    """Logout user"""
    # Clear session state
    keys_to_clear = ["jwt_token", "user", "page"]
    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

    st.session_state.page = "home"
    st.success("Logged out successfully")
    st.rerun()
