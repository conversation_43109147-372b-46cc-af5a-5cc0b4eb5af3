import google.generativeai as genai
from config.settings import settings
from models.quiz import EssayGradingRequest
from typing import Dict, Any
import json
import re

# Configure Gemini API
if settings.GEMINI_API_KEY:
    genai.configure(api_key=settings.GEMINI_API_KEY)

class EssayGrader:
    def __init__(self):
        self.model = None
        if settings.GEMINI_API_KEY:
            self.model = genai.GenerativeModel('gemini-pro')
    
    async def grade_essay(self, request: EssayGradingRequest) -> Dict[str, Any]:
        """Grade an essay using Gemini API"""
        if not self.model:
            return self._generate_mock_grade(request)
        
        try:
            prompt = self._create_grading_prompt(request)
            response = self.model.generate_content(prompt)
            
            # Parse the response
            grading_result = self._parse_grading_response(response.text, request.max_score)
            return grading_result
            
        except Exception as e:
            print(f"Error grading essay: {e}")
            return self._generate_mock_grade(request)
    
    def _create_grading_prompt(self, request: EssayGradingRequest) -> str:
        """Create prompt for essay grading"""
        return f"""
        Please grade the following essay based on the provided rubric. 
        Provide a detailed evaluation with scores for different criteria.
        
        ESSAY TO GRADE:
        {request.essay_text}
        
        GRADING RUBRIC:
        {request.rubric}
        
        MAXIMUM SCORE: {request.max_score}
        
        Please provide your evaluation in the following JSON format:
        {{
            "overall_score": <numeric score out of {request.max_score}>,
            "percentage": <percentage score>,
            "criteria_scores": {{
                "content": <score for content quality>,
                "organization": <score for organization>,
                "grammar": <score for grammar and language>,
                "creativity": <score for creativity/originality>
            }},
            "strengths": [
                "List of essay strengths"
            ],
            "areas_for_improvement": [
                "List of areas that need improvement"
            ],
            "detailed_feedback": "Detailed written feedback explaining the grade",
            "suggestions": [
                "Specific suggestions for improvement"
            ]
        }}
        
        Be constructive and specific in your feedback. Focus on helping the student improve.
        """
    
    def _parse_grading_response(self, response_text: str, max_score: int) -> Dict[str, Any]:
        """Parse Gemini grading response"""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                grading_data = json.loads(json_str)
                
                # Validate and clean the data
                overall_score = min(max(grading_data.get("overall_score", 0), 0), max_score)
                percentage = (overall_score / max_score) * 100
                
                return {
                    "overall_score": overall_score,
                    "max_score": max_score,
                    "percentage": round(percentage, 2),
                    "criteria_scores": grading_data.get("criteria_scores", {}),
                    "strengths": grading_data.get("strengths", []),
                    "areas_for_improvement": grading_data.get("areas_for_improvement", []),
                    "detailed_feedback": grading_data.get("detailed_feedback", ""),
                    "suggestions": grading_data.get("suggestions", []),
                    "graded_by": "AI Assistant",
                    "grading_method": "Gemini AI"
                }
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            print(f"Error parsing grading response: {e}")
            return self._generate_mock_grade(EssayGradingRequest(
                essay_text="Sample essay",
                rubric="Standard rubric",
                max_score=max_score
            ))
    
    def _generate_mock_grade(self, request: EssayGradingRequest) -> Dict[str, Any]:
        """Generate mock grading when API is not available"""
        # Simple heuristic grading based on essay length and basic analysis
        essay_length = len(request.essay_text.split())
        
        # Basic scoring heuristics
        if essay_length < 50:
            base_score = 0.4
        elif essay_length < 150:
            base_score = 0.6
        elif essay_length < 300:
            base_score = 0.75
        else:
            base_score = 0.85
        
        # Check for basic quality indicators
        has_paragraphs = '\n' in request.essay_text or len(request.essay_text.split('.')) > 3
        has_good_length = 100 <= essay_length <= 500
        
        if has_paragraphs:
            base_score += 0.05
        if has_good_length:
            base_score += 0.05
        
        overall_score = min(base_score * request.max_score, request.max_score)
        percentage = (overall_score / request.max_score) * 100
        
        return {
            "overall_score": round(overall_score, 1),
            "max_score": request.max_score,
            "percentage": round(percentage, 2),
            "criteria_scores": {
                "content": round(overall_score * 0.4, 1),
                "organization": round(overall_score * 0.3, 1),
                "grammar": round(overall_score * 0.2, 1),
                "creativity": round(overall_score * 0.1, 1)
            },
            "strengths": [
                "Demonstrates understanding of the topic",
                "Appropriate essay length" if has_good_length else "Clear attempt at addressing the question"
            ],
            "areas_for_improvement": [
                "Could benefit from more detailed examples",
                "Consider improving paragraph structure" if not has_paragraphs else "Continue developing writing skills"
            ],
            "detailed_feedback": f"This essay shows a {self._get_performance_level(percentage)} understanding of the topic. "
                               f"The response is {'well-structured' if has_paragraphs else 'adequately structured'} "
                               f"and demonstrates {'good' if essay_length > 200 else 'basic'} engagement with the subject matter.",
            "suggestions": [
                "Include more specific examples to support your points",
                "Consider using transitional phrases between paragraphs",
                "Proofread for grammar and spelling errors"
            ],
            "graded_by": "AI Assistant (Mock Mode)",
            "grading_method": "Heuristic Analysis"
        }
    
    def _get_performance_level(self, percentage: float) -> str:
        """Get performance level description"""
        if percentage >= 90:
            return "excellent"
        elif percentage >= 80:
            return "good"
        elif percentage >= 70:
            return "satisfactory"
        elif percentage >= 60:
            return "basic"
        else:
            return "developing"

essay_grader = EssayGrader()
