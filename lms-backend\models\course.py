from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class CourseBase(BaseModel):
    title: str
    description: str
    category: str
    difficulty_level: str  # beginner, intermediate, advanced

class CourseCreate(CourseBase):
    pass

class CourseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    difficulty_level: Optional[str] = None

class CourseInDB(CourseBase):
    id: str = Field(alias="_id")
    professor_id: str
    professor_name: str
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    enrolled_students: List[str] = []

class CourseResponse(CourseBase):
    id: str = Field(alias="_id")
    professor_id: str
    professor_name: str
    created_at: datetime
    enrolled_count: int = 0

class EnrollmentBase(BaseModel):
    student_id: str
    course_id: str

class EnrollmentCreate(EnrollmentBase):
    pass

class EnrollmentInDB(EnrollmentBase):
    id: str = Field(alias="_id")
    enrolled_at: datetime
    progress: float = 0.0  # 0-100%
    is_active: bool = True
