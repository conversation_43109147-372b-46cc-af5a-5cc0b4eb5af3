import google.generativeai as genai
from config.settings import settings
import pandas as pd
from typing import Dict, Any, List
import json
import re

# Configure Gemini API
if settings.GEMINI_API_KEY:
    genai.configure(api_key=settings.GEMINI_API_KEY)

class PerformanceSummarizer:
    def __init__(self):
        self.model = None
        if settings.GEMINI_API_KEY:
            self.model = genai.GenerativeModel('gemini-pro')
    
    async def summarize_performance(self, performance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate performance summary using Gemini API"""
        if not self.model:
            return self._generate_mock_summary(performance_data)
        
        try:
            # Prepare data for analysis
            df = pd.DataFrame(performance_data)
            data_summary = self._prepare_data_summary(df)
            
            prompt = self._create_summary_prompt(data_summary)
            response = self.model.generate_content(prompt)
            
            # Parse the response
            summary_result = self._parse_summary_response(response.text, df)
            return summary_result
            
        except Exception as e:
            print(f"Error generating performance summary: {e}")
            return self._generate_mock_summary(performance_data)
    
    def _prepare_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Prepare statistical summary of the data"""
        try:
            summary = {
                "total_students": len(df),
                "total_courses": df['course_id'].nunique() if 'course_id' in df.columns else 0,
                "average_score": df['score'].mean() if 'score' in df.columns else 0,
                "score_distribution": {
                    "excellent (90-100)": len(df[df['score'] >= 90]) if 'score' in df.columns else 0,
                    "good (80-89)": len(df[(df['score'] >= 80) & (df['score'] < 90)]) if 'score' in df.columns else 0,
                    "satisfactory (70-79)": len(df[(df['score'] >= 70) & (df['score'] < 80)]) if 'score' in df.columns else 0,
                    "needs_improvement (<70)": len(df[df['score'] < 70]) if 'score' in df.columns else 0
                },
                "top_performing_courses": [],
                "struggling_areas": [],
                "engagement_metrics": {}
            }
            
            # Course performance analysis
            if 'course_id' in df.columns and 'score' in df.columns:
                course_performance = df.groupby('course_id')['score'].agg(['mean', 'count']).reset_index()
                course_performance = course_performance.sort_values('mean', ascending=False)
                
                summary["top_performing_courses"] = [
                    {
                        "course_id": row['course_id'],
                        "average_score": round(row['mean'], 2),
                        "student_count": row['count']
                    }
                    for _, row in course_performance.head(5).iterrows()
                ]
                
                # Identify struggling areas
                struggling_courses = course_performance[course_performance['mean'] < 70]
                summary["struggling_areas"] = [
                    {
                        "course_id": row['course_id'],
                        "average_score": round(row['mean'], 2),
                        "student_count": row['count']
                    }
                    for _, row in struggling_courses.iterrows()
                ]
            
            # Engagement metrics
            if 'attendance_rate' in df.columns:
                summary["engagement_metrics"]["average_attendance"] = df['attendance_rate'].mean()
            if 'assignment_completion' in df.columns:
                summary["engagement_metrics"]["average_assignment_completion"] = df['assignment_completion'].mean()
            
            return summary
            
        except Exception as e:
            print(f"Error preparing data summary: {e}")
            return {"total_students": len(df), "error": str(e)}
    
    def _create_summary_prompt(self, data_summary: Dict[str, Any]) -> str:
        """Create prompt for performance summarization"""
        return f"""
        Analyze the following student performance data and provide comprehensive insights:
        
        DATA SUMMARY:
        {json.dumps(data_summary, indent=2)}
        
        Please provide your analysis in the following JSON format:
        {{
            "overall_performance": {{
                "grade": "<A/B/C/D/F>",
                "description": "Brief overall assessment"
            }},
            "key_insights": [
                "List of 3-5 key insights from the data"
            ],
            "strengths": [
                "Areas where students are performing well"
            ],
            "concerns": [
                "Areas that need attention"
            ],
            "recommendations": [
                "Specific actionable recommendations for improvement"
            ],
            "trends": [
                "Notable trends or patterns in the data"
            ],
            "action_items": [
                "Immediate action items for administrators/professors"
            ]
        }}
        
        Focus on actionable insights that can help improve student outcomes.
        """
    
    def _parse_summary_response(self, response_text: str, df: pd.DataFrame) -> Dict[str, Any]:
        """Parse Gemini summary response"""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                summary_data = json.loads(json_str)
                
                # Add statistical data
                summary_data["statistics"] = {
                    "total_students": len(df),
                    "average_score": round(df['score'].mean(), 2) if 'score' in df.columns else 0,
                    "median_score": round(df['score'].median(), 2) if 'score' in df.columns else 0,
                    "score_std": round(df['score'].std(), 2) if 'score' in df.columns else 0,
                    "pass_rate": round((len(df[df['score'] >= 70]) / len(df)) * 100, 2) if 'score' in df.columns else 0
                }
                
                summary_data["generated_by"] = "Gemini AI"
                summary_data["analysis_date"] = pd.Timestamp.now().isoformat()
                
                return summary_data
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            print(f"Error parsing summary response: {e}")
            return self._generate_mock_summary([])
    
    def _generate_mock_summary(self, performance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate mock summary when API is not available"""
        try:
            df = pd.DataFrame(performance_data) if performance_data else pd.DataFrame()
            
            if len(df) == 0:
                return {
                    "overall_performance": {
                        "grade": "N/A",
                        "description": "No data available for analysis"
                    },
                    "key_insights": ["No performance data available"],
                    "strengths": [],
                    "concerns": ["Insufficient data for analysis"],
                    "recommendations": ["Collect more student performance data"],
                    "trends": [],
                    "action_items": ["Set up data collection systems"],
                    "statistics": {
                        "total_students": 0,
                        "average_score": 0,
                        "median_score": 0,
                        "score_std": 0,
                        "pass_rate": 0
                    },
                    "generated_by": "Mock Analysis",
                    "analysis_date": pd.Timestamp.now().isoformat()
                }
            
            # Calculate basic statistics
            avg_score = df['score'].mean() if 'score' in df.columns else 75
            pass_rate = (len(df[df['score'] >= 70]) / len(df)) * 100 if 'score' in df.columns else 80
            
            # Determine overall grade
            if avg_score >= 90:
                grade = "A"
                description = "Excellent overall performance"
            elif avg_score >= 80:
                grade = "B"
                description = "Good overall performance"
            elif avg_score >= 70:
                grade = "C"
                description = "Satisfactory performance"
            elif avg_score >= 60:
                grade = "D"
                description = "Below average performance"
            else:
                grade = "F"
                description = "Poor performance requiring immediate attention"
            
            return {
                "overall_performance": {
                    "grade": grade,
                    "description": description
                },
                "key_insights": [
                    f"Average student score is {avg_score:.1f}%",
                    f"Pass rate is {pass_rate:.1f}%",
                    f"Total of {len(df)} students analyzed"
                ],
                "strengths": [
                    "Students show consistent engagement" if pass_rate > 75 else "Some students performing well",
                    "Good participation in assessments"
                ],
                "concerns": [
                    "Some students need additional support" if pass_rate < 80 else "Minor performance gaps",
                    "Consider reviewing course difficulty"
                ],
                "recommendations": [
                    "Implement targeted support for struggling students",
                    "Consider additional practice materials",
                    "Review assessment methods for effectiveness"
                ],
                "trends": [
                    "Performance varies across different topics",
                    "Engagement levels are generally positive"
                ],
                "action_items": [
                    "Schedule one-on-one sessions with low-performing students",
                    "Review and update course materials",
                    "Implement peer tutoring programs"
                ],
                "statistics": {
                    "total_students": len(df),
                    "average_score": round(avg_score, 2),
                    "median_score": round(df['score'].median(), 2) if 'score' in df.columns else round(avg_score, 2),
                    "score_std": round(df['score'].std(), 2) if 'score' in df.columns else 10.0,
                    "pass_rate": round(pass_rate, 2)
                },
                "generated_by": "Mock Analysis",
                "analysis_date": pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error generating mock summary: {e}")
            return {
                "overall_performance": {"grade": "N/A", "description": "Error in analysis"},
                "key_insights": [f"Analysis error: {str(e)}"],
                "strengths": [],
                "concerns": ["Data analysis failed"],
                "recommendations": ["Check data format and try again"],
                "trends": [],
                "action_items": ["Review data collection process"],
                "statistics": {"total_students": 0, "average_score": 0, "median_score": 0, "score_std": 0, "pass_rate": 0},
                "generated_by": "Error Handler",
                "analysis_date": pd.Timestamp.now().isoformat()
            }

performance_summarizer = PerformanceSummarizer()
