import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import pandas as pd

from utils.auth import require_role
from services.api import api_client

# Page configuration
st.set_page_config(
    page_title="Student Dashboard - LMS",
    page_icon="👨‍🎓",
    layout="wide"
)

# Require student role
require_role("student")

# Custom CSS
st.markdown("""
<style>
    .student-header {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        padding: 1.5rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .course-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
        margin-bottom: 1rem;
    }
    
    .course-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
    
    .progress-bar {
        background-color: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 10px 0;
    }
    
    .progress-fill {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    user = st.session_state.get("user", {})
    st.markdown(f"""
    <div class="student-header">
        <h1>👨‍🎓 Welcome, {user.get('full_name', 'Student')}!</h1>
        <p>Your Learning Dashboard</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Load dashboard data
    try:
        with st.spinner("Loading your dashboard..."):
            stats = api_client.get_student_dashboard_stats()
            enrolled_courses = api_client.get_enrolled_courses()
            quiz_results = api_client.get_quiz_results()
    except Exception as e:
        st.error(f"Error loading dashboard data: {e}")
        return
    
    # Key Metrics
    st.markdown("### 📊 Your Progress")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        enrolled_count = stats["enrollment_statistics"]["enrolled_courses"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{enrolled_count}</div>
            <div style="font-size: 1rem; opacity: 0.9;">Enrolled Courses</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        total_quizzes = stats["quiz_statistics"]["total_quizzes_taken"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_quizzes}</div>
            <div style="font-size: 1rem; opacity: 0.9;">Quizzes Taken</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        avg_score = stats["quiz_statistics"]["average_score"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{avg_score}%</div>
            <div style="font-size: 1rem; opacity: 0.9;">Average Score</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        highest_score = stats["quiz_statistics"]["highest_score"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{highest_score}%</div>
            <div style="font-size: 1rem; opacity: 0.9;">Best Score</div>
        </div>
        """, unsafe_allow_html=True)
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Enrolled Courses
        st.markdown("### 📚 My Courses")
        
        if enrolled_courses:
            for course in enrolled_courses:
                st.markdown(f"""
                <div class="course-card">
                    <h4>{course['title']}</h4>
                    <p><strong>Professor:</strong> {course['professor_name']}</p>
                    <p><strong>Category:</strong> {course['category']}</p>
                    <p><strong>Difficulty:</strong> {course['difficulty_level'].title()}</p>
                    <p>{course['description'][:100]}...</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%;"></div>
                    </div>
                    <small>Progress: 75% Complete</small>
                </div>
                """, unsafe_allow_html=True)
                
                col_quiz, col_view = st.columns(2)
                with col_quiz:
                    if st.button(f"📝 Take Quiz", key=f"quiz_{course['id']}", use_container_width=True):
                        st.session_state.selected_course_id = course['id']
                        st.switch_page("pages/student_quiz.py")
                
                with col_view:
                    if st.button(f"👁️ View Course", key=f"view_{course['id']}", use_container_width=True):
                        st.session_state.selected_course = course
                        st.switch_page("pages/student_course_detail.py")
        else:
            st.info("You haven't enrolled in any courses yet.")
            if st.button("🔍 Browse Courses", use_container_width=True):
                st.switch_page("pages/student_courses.py")
    
    with col2:
        # Quick Actions
        st.markdown("### ⚡ Quick Actions")
        
        if st.button("🔍 Browse Courses", use_container_width=True, type="primary"):
            st.switch_page("pages/student_courses.py")
        
        if st.button("📝 Take Quiz", use_container_width=True):
            st.switch_page("pages/student_quiz.py")
        
        if st.button("📊 View Results", use_container_width=True):
            st.switch_page("pages/student_results.py")
        
        if st.button("💡 Get Recommendations", use_container_width=True):
            st.switch_page("pages/student_recommendations.py")
        
        # Recent Quiz Results
        st.markdown("### 📈 Recent Quiz Results")
        
        if quiz_results:
            recent_results = sorted(quiz_results, key=lambda x: x['submitted_at'], reverse=True)[:5]
            
            for result in recent_results:
                score_color = "#28a745" if result['percentage'] >= 70 else "#dc3545"
                st.markdown(f"""
                <div style="background: white; padding: 1rem; border-radius: 8px; margin-bottom: 0.5rem; border-left: 4px solid {score_color};">
                    <strong>{result.get('quiz_title', 'Quiz')}</strong><br>
                    <span style="color: {score_color}; font-weight: bold;">{result['percentage']:.1f}%</span>
                    <small style="color: #6c757d; display: block;">
                        {result['correct_answers']}/{result['total_questions']} correct
                    </small>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("No quiz results yet. Take your first quiz!")
    
    # Performance Chart
    if stats["quiz_statistics"]["recent_scores"]:
        st.markdown("### 📈 Performance Trend")
        
        recent_scores = stats["quiz_statistics"]["recent_scores"]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=list(range(1, len(recent_scores) + 1)),
            y=recent_scores,
            mode='lines+markers',
            name='Quiz Scores',
            line=dict(color='#28a745', width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title="Recent Quiz Performance",
            xaxis_title="Quiz Number",
            yaxis_title="Score (%)",
            height=400,
            showlegend=False,
            yaxis=dict(range=[0, 100])
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Study Tips
    with st.expander("💡 Study Tips"):
        st.markdown("""
        ### Improve Your Learning
        
        **📚 Study Strategies:**
        - Review course materials regularly
        - Take practice quizzes frequently
        - Join study groups with classmates
        - Ask questions during lectures
        
        **🎯 Goal Setting:**
        - Set weekly learning goals
        - Track your progress consistently
        - Celebrate small achievements
        - Focus on understanding, not just memorizing
        
        **⏰ Time Management:**
        - Create a study schedule
        - Use the Pomodoro Technique
        - Take regular breaks
        - Prioritize difficult topics
        """)
    
    # Navigation
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🏠 Home", use_container_width=True):
            st.switch_page("main.py")
    
    with col2:
        if st.button("📚 Courses", use_container_width=True):
            st.switch_page("pages/student_courses.py")
    
    with col3:
        if st.button("📝 Quizzes", use_container_width=True):
            st.switch_page("pages/student_quiz.py")
    
    with col4:
        if st.button("📊 Results", use_container_width=True):
            st.switch_page("pages/student_results.py")

if __name__ == "__main__":
    main()
