Build me a full-stack Learning Management System (LMS) Web Application with the following upgraded specifications:

---

## 🔐 Authentication:
- Implement secure login & registration using JWT (JSON Web Tokens)
- Use FastAPI for issuing & verifying tokens
- Store JWT in localStorage (React)
- Validate role on each request
- Roles:
  - Admin
  - Professor
  - Student

---

## 📦 Backend Stack (FastAPI + Python):
- FastAPI for backend API framework
- SQLAlchemy ORM with PostgreSQL or SQLite
- Modular folder structure with separate route files:
  - `routes/admin.py`
  - `routes/professor.py`
  - `routes/student.py`
- Use `Pydantic` models and schema validation
- Token-based access control per role

---

## 🧠 AI/ML Integration (Python):

### 1. Quiz Generator:
- Use Gemini Pro API (via `google.generativeai`) to generate multiple-choice quizzes based on course topics.
- Endpoint: `/professor/generate-quiz`
- Input: Topic name, difficulty
- Output: JSON with 5 MCQs

### 2. Score Predictor:
- Use `scikit-learn` regression/classification model
- Train on mock quiz data (score, attempts, time spent)
- Endpoint: `/professor/predict-score`
- Return: Expected performance (grade or score)

### 3. Course Recommender:
- Use clustering (KMeans) or cosine similarity
- Input: Past performance, enrolled courses
- Output: Personalized course suggestions
- Endpoint: `/student/recommend`

### 4. Admin Performance Summarizer:
- Use Gemini Pro to generate human-like insights from performance data
- Endpoint: `/admin/summarize-performance`
- Output: Natural language summary (e.g., “Student performance dropped 12%...")

### 5. Essay Auto-Grader:
- NLP-based scoring using Gemini
- Professors input rubric, students upload essay
- Endpoint: `/professor/grade-essay`

### 6. Flashcard Generator:
- Convert lecture content or video transcript into flashcards using Gemini

### 7. AI Tutor Chatbot (optional):
- Student sends question → Gemini returns personalized explanation

### 8. Cheat Detection (Bonus):
- Analyze test-taking patterns using AI (timing, input behavior)
- Return probability of cheating

---

## 🌐 Frontend Stack (ReactJS):
- React (Functional Components)
- React Router for navigation
- TailwindCSS + DaisyUI for clean UI
- Axios for backend communication
- JWT stored in localStorage
- Protected routes based on user role

---

## 🖼️ Frontend Pages:

### 🔐 Auth
- Login Page
- Register Page

### 👑 Admin Dashboard
- View All Users
- Upload/Import CSV Users
- System Analytics (student activity, quiz attempts)
- Performance Summary (AI-generated via Gemini)
- Role Manager (CRUD)

### 👨‍🏫 Professor Dashboard
- Upload Courses
- Generate Quizzes using Gemini
- Upload Lectures / Videos
- Predict Student Scores (ML)
- Auto-Grade Essays
- View Student Analytics

### 👨‍🎓 Student Dashboard
- View Enrolled Courses
- Take Quiz
- Submit Essay / Assignments
- Track Progress
- Get AI-Based Recommendations
- Ask AI Tutor (chat)
- Earn Badges & XP (gamification)

---

## 🧩 Optional Cool Features:
- 🌗 Dark Mode toggle
- 🧠 Personalized learning paths via digital twin
- ⏯️ Resume video tracking
- 📅 Calendar with deadlines & auto reminders (cron jobs / Celery)
- 🔔 Real-time notifications using FastAPI WebSockets
- 💬 Chat with professor / support team
- 📜 Blockchain certificate issuance (optional)

---

## 📁 Project Structure

### Backend
backend/
├── main.py
├── auth/
│ └── jwt_handler.py
├── routes/
│ ├── admin.py
│ ├── professor.py
│ └── student.py
├── ml/
│ ├── quiz_gen.py
│ ├── predict_score.py
│ ├── recommender.py
│ ├── essay_grader.py
│ └── summary.py
├── models/
│ ├── user.py
│ ├── course.py
│ ├── quiz.py
│ └── performance.py
├── database/
│ └── db.py
├── utils/
│ ├── validator.py
│ └── file_parser.py
├── requirements.txt

shell
Copy
Edit

### Frontend
frontend/
├── src/
│ ├── pages/
│ │ ├── Login.jsx
│ │ ├── Register.jsx
│ │ ├── AdminDashboard.jsx
│ │ ├── ProfessorDashboard.jsx
│ │ ├── StudentDashboard.jsx
│ ├── components/
│ │ ├── Navbar.jsx
│ │ ├── Sidebar.jsx
│ │ ├── Flashcard.jsx
│ │ ├── QuizCard.jsx
│ │ ├── VideoPlayer.jsx
│ │ └── RecommendationBox.jsx
│ ├── services/
│ │ └── api.js
│ ├── utils/
│ │ └── auth.js
│ ├── App.jsx
│ ├── main.jsx
│ └── router.jsx
├── tailwind.config.js
├── postcss.config.js
└── index.html

yaml
Copy
Edit

---

## 🛠 Dev Guidelines
- Backend must return standardized JSON responses
- All routes protected by JWT and role-based access
- React must use loading/error states
- Use dummy data for local ML models (CSV)
- Keep code modular and readable
- Provide instructions for `.env` setup and Gemini API key

---

## 🔥 Extras
- Seed script for dummy users/courses
- Mock Gemini response if API key not provided
- Docker setup optional
- Deploy-ready instructions (e.g., Vercel + Render)

---

🎯 Final Outcome: A modular, AI-powered LMS with advanced student personalization, AI-generated quizzes and feedback, real-time engagement analytics, and smooth frontend UX — ready to ship or scale.
