import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import pandas as pd

from utils.auth import require_role
from services.api import api_client

# Page configuration
st.set_page_config(
    page_title="Admin Dashboard - LMS",
    page_icon="👑",
    layout="wide"
)

# Require admin role
require_role("admin")

# Custom CSS
st.markdown("""
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .admin-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .quick-action-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .quick-action-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="admin-header">
        <h1>👑 Admin Dashboard</h1>
        <p>System Management & Analytics</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Load dashboard data
    try:
        with st.spinner("Loading dashboard data..."):
            stats = api_client.get_admin_dashboard_stats()
    except Exception as e:
        st.error(f"Error loading dashboard data: {e}")
        return
    
    # Key Metrics
    st.markdown("### 📊 System Overview")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_users = sum(stats["user_statistics"].values())
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{total_users}</div>
            <div class="metric-label">Total Users</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        total_courses = stats["course_statistics"]["total_courses"]
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{total_courses}</div>
            <div class="metric-label">Total Courses</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        total_attempts = stats["quiz_statistics"]["total_attempts"]
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{total_attempts}</div>
            <div class="metric-label">Quiz Attempts</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        new_users = stats["recent_activity"]["new_users_this_week"]
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{new_users}</div>
            <div class="metric-label">New Users (7d)</div>
        </div>
        """, unsafe_allow_html=True)
    
    # Charts Section
    st.markdown("### 📈 Analytics")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # User distribution pie chart
        user_stats = stats["user_statistics"]
        if sum(user_stats.values()) > 0:
            fig_users = px.pie(
                values=list(user_stats.values()),
                names=[role.title() for role in user_stats.keys()],
                title="User Distribution by Role",
                color_discrete_sequence=px.colors.qualitative.Set3
            )
            fig_users.update_layout(
                showlegend=True,
                height=400,
                font=dict(size=12)
            )
            st.plotly_chart(fig_users, use_container_width=True)
        else:
            st.info("No user data available")
    
    with col2:
        # Course activity chart
        course_stats = stats["course_statistics"]
        active_courses = course_stats["active_courses"]
        inactive_courses = course_stats["total_courses"] - active_courses
        
        if course_stats["total_courses"] > 0:
            fig_courses = go.Figure(data=[
                go.Bar(
                    x=['Active', 'Inactive'],
                    y=[active_courses, inactive_courses],
                    marker_color=['#28a745', '#dc3545']
                )
            ])
            fig_courses.update_layout(
                title="Course Status",
                xaxis_title="Status",
                yaxis_title="Number of Courses",
                height=400,
                showlegend=False
            )
            st.plotly_chart(fig_courses, use_container_width=True)
        else:
            st.info("No course data available")
    
    # Quick Actions
    st.markdown("### ⚡ Quick Actions")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div class="quick-action-card">
            <h4>👥 Manage Users</h4>
            <p>Add, edit, or remove users</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("Manage Users", use_container_width=True, key="manage_users"):
            st.switch_page("pages/admin_users.py")
    
    with col2:
        st.markdown("""
        <div class="quick-action-card">
            <h4>📊 Performance Analysis</h4>
            <p>Upload data and generate insights</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("Performance Analysis", use_container_width=True, key="performance"):
            st.switch_page("pages/admin_performance.py")
    
    with col3:
        st.markdown("""
        <div class="quick-action-card">
            <h4>📚 View All Courses</h4>
            <p>Browse all system courses</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("View Courses", use_container_width=True, key="view_courses"):
            try:
                courses = api_client.get_all_courses()
                st.session_state.admin_courses = courses
                st.switch_page("pages/admin_courses.py")
            except Exception as e:
                st.error(f"Error loading courses: {e}")
    
    with col4:
        st.markdown("""
        <div class="quick-action-card">
            <h4>🔧 System Settings</h4>
            <p>Configure system parameters</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("System Settings", use_container_width=True, key="settings"):
            st.info("System settings feature coming soon!")
    
    # Recent Activity
    st.markdown("### 🕒 Recent Activity")
    
    recent_activity = stats["recent_activity"]
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            label="New Users This Week",
            value=recent_activity["new_users_this_week"],
            delta=f"+{recent_activity['new_users_this_week']} from last week"
        )
    
    with col2:
        st.metric(
            label="Quiz Attempts This Week",
            value=recent_activity["quiz_attempts_this_week"],
            delta=f"+{recent_activity['quiz_attempts_this_week']} from last week"
        )
    
    # System Information
    with st.expander("ℹ️ System Information"):
        st.markdown(f"""
        **Dashboard Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        **System Statistics:**
        - Total Users: {total_users}
        - Students: {user_stats.get('student', 0)}
        - Professors: {user_stats.get('professor', 0)}
        - Admins: {user_stats.get('admin', 0)}
        
        **Course Statistics:**
        - Total Courses: {course_stats['total_courses']}
        - Active Courses: {course_stats['active_courses']}
        
        **Quiz Statistics:**
        - Total Quiz Attempts: {stats['quiz_statistics']['total_attempts']}
        - Recent Quiz Attempts: {recent_activity['quiz_attempts_this_week']}
        """)
    
    # Navigation
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🏠 Home", use_container_width=True):
            st.switch_page("main.py")
    
    with col2:
        if st.button("👥 Users", use_container_width=True):
            st.switch_page("pages/admin_users.py")
    
    with col3:
        if st.button("📈 Analytics", use_container_width=True):
            st.switch_page("pages/admin_performance.py")

if __name__ == "__main__":
    main()
