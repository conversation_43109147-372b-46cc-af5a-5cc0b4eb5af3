import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from typing import List, Dict, Any
import asyncio

class CourseRecommender:
    def __init__(self):
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.course_features = None
        self.courses_df = None
        self.user_clusters = None
        self.kmeans_model = None
    
    async def initialize_with_courses(self, courses_data: List[Dict[str, Any]]):
        """Initialize recommender with course data"""
        try:
            self.courses_df = pd.DataFrame(courses_data)
            
            if len(self.courses_df) == 0:
                print("No courses available for recommendation")
                return
            
            # Create course features from title, description, and category
            course_text = (
                self.courses_df['title'].fillna('') + ' ' +
                self.courses_df['description'].fillna('') + ' ' +
                self.courses_df['category'].fillna('') + ' ' +
                self.courses_df['difficulty_level'].fillna('')
            )
            
            # Create TF-IDF features
            self.course_features = self.tfidf_vectorizer.fit_transform(course_text)
            
            # Create user clusters based on course preferences (mock implementation)
            if len(self.courses_df) >= 3:
                self.kmeans_model = KMeans(n_clusters=min(3, len(self.courses_df)), random_state=42)
                self.user_clusters = self.kmeans_model.fit_predict(self.course_features.toarray())
            
            print(f"Course recommender initialized with {len(self.courses_df)} courses")
            
        except Exception as e:
            print(f"Error initializing course recommender: {e}")
    
    async def get_content_based_recommendations(
        self, 
        user_enrolled_courses: List[str], 
        num_recommendations: int = 5
    ) -> List[Dict[str, Any]]:
        """Get course recommendations based on content similarity"""
        try:
            if self.courses_df is None or len(self.courses_df) == 0:
                return []
            
            # Get enrolled course indices
            enrolled_indices = []
            for course_id in user_enrolled_courses:
                matching_courses = self.courses_df[self.courses_df['id'] == course_id]
                if not matching_courses.empty:
                    enrolled_indices.append(matching_courses.index[0])
            
            if not enrolled_indices:
                # If no enrolled courses, recommend popular/diverse courses
                return self._get_diverse_recommendations(num_recommendations)
            
            # Calculate average feature vector for enrolled courses
            enrolled_features = self.course_features[enrolled_indices]
            avg_user_profile = np.mean(enrolled_features.toarray(), axis=0)
            
            # Calculate similarity with all courses
            similarities = cosine_similarity([avg_user_profile], self.course_features.toarray())[0]
            
            # Get course indices sorted by similarity (excluding enrolled courses)
            available_indices = [i for i in range(len(self.courses_df)) if i not in enrolled_indices]
            available_similarities = [(i, similarities[i]) for i in available_indices]
            available_similarities.sort(key=lambda x: x[1], reverse=True)
            
            # Get top recommendations
            recommendations = []
            for i, similarity in available_similarities[:num_recommendations]:
                course = self.courses_df.iloc[i]
                recommendations.append({
                    "course_id": course['id'],
                    "title": course['title'],
                    "description": course['description'],
                    "category": course['category'],
                    "difficulty_level": course['difficulty_level'],
                    "similarity_score": round(similarity, 3),
                    "recommendation_reason": "Based on your enrolled courses"
                })
            
            return recommendations
            
        except Exception as e:
            print(f"Error getting content-based recommendations: {e}")
            return []
    
    async def get_collaborative_recommendations(
        self, 
        user_id: str, 
        user_enrollments: List[str],
        all_user_enrollments: Dict[str, List[str]],
        num_recommendations: int = 5
    ) -> List[Dict[str, Any]]:
        """Get recommendations based on similar users (collaborative filtering)"""
        try:
            if not all_user_enrollments or len(all_user_enrollments) < 2:
                return []
            
            # Find similar users based on course overlap
            user_similarities = {}
            user_courses = set(user_enrollments)
            
            for other_user_id, other_courses in all_user_enrollments.items():
                if other_user_id == user_id:
                    continue
                
                other_courses_set = set(other_courses)
                
                # Calculate Jaccard similarity
                intersection = len(user_courses.intersection(other_courses_set))
                union = len(user_courses.union(other_courses_set))
                
                if union > 0:
                    similarity = intersection / union
                    user_similarities[other_user_id] = similarity
            
            # Get top similar users
            similar_users = sorted(user_similarities.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # Collect course recommendations from similar users
            recommended_courses = {}
            for similar_user_id, similarity in similar_users:
                for course_id in all_user_enrollments[similar_user_id]:
                    if course_id not in user_enrollments:
                        if course_id not in recommended_courses:
                            recommended_courses[course_id] = 0
                        recommended_courses[course_id] += similarity
            
            # Sort recommendations by score
            sorted_recommendations = sorted(
                recommended_courses.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:num_recommendations]
            
            # Get course details
            recommendations = []
            for course_id, score in sorted_recommendations:
                matching_courses = self.courses_df[self.courses_df['id'] == course_id]
                if not matching_courses.empty:
                    course = matching_courses.iloc[0]
                    recommendations.append({
                        "course_id": course_id,
                        "title": course['title'],
                        "description": course['description'],
                        "category": course['category'],
                        "difficulty_level": course['difficulty_level'],
                        "similarity_score": round(score, 3),
                        "recommendation_reason": "Based on similar students"
                    })
            
            return recommendations
            
        except Exception as e:
            print(f"Error getting collaborative recommendations: {e}")
            return []
    
    def _get_diverse_recommendations(self, num_recommendations: int) -> List[Dict[str, Any]]:
        """Get diverse course recommendations for new users"""
        try:
            if self.courses_df is None or len(self.courses_df) == 0:
                return []
            
            # Sample diverse courses from different categories and difficulty levels
            recommendations = []
            
            # Group by category and difficulty
            categories = self.courses_df['category'].unique()
            difficulties = self.courses_df['difficulty_level'].unique()
            
            for category in categories[:3]:  # Top 3 categories
                for difficulty in difficulties[:2]:  # Top 2 difficulties
                    matching_courses = self.courses_df[
                        (self.courses_df['category'] == category) & 
                        (self.courses_df['difficulty_level'] == difficulty)
                    ]
                    
                    if not matching_courses.empty:
                        course = matching_courses.iloc[0]
                        recommendations.append({
                            "course_id": course['id'],
                            "title": course['title'],
                            "description": course['description'],
                            "category": course['category'],
                            "difficulty_level": course['difficulty_level'],
                            "similarity_score": 0.5,
                            "recommendation_reason": "Popular course in this category"
                        })
                        
                        if len(recommendations) >= num_recommendations:
                            break
                
                if len(recommendations) >= num_recommendations:
                    break
            
            return recommendations[:num_recommendations]
            
        except Exception as e:
            print(f"Error getting diverse recommendations: {e}")
            return []
    
    async def get_hybrid_recommendations(
        self,
        user_id: str,
        user_enrolled_courses: List[str],
        all_user_enrollments: Dict[str, List[str]],
        num_recommendations: int = 5
    ) -> List[Dict[str, Any]]:
        """Get hybrid recommendations combining content-based and collaborative filtering"""
        try:
            # Get content-based recommendations
            content_recs = await self.get_content_based_recommendations(
                user_enrolled_courses, 
                num_recommendations
            )
            
            # Get collaborative recommendations
            collab_recs = await self.get_collaborative_recommendations(
                user_id,
                user_enrolled_courses,
                all_user_enrollments,
                num_recommendations
            )
            
            # Combine and deduplicate recommendations
            combined_recs = {}
            
            # Add content-based recommendations with weight 0.6
            for rec in content_recs:
                course_id = rec['course_id']
                combined_recs[course_id] = {
                    **rec,
                    'combined_score': rec['similarity_score'] * 0.6,
                    'recommendation_reason': 'Content-based: ' + rec['recommendation_reason']
                }
            
            # Add collaborative recommendations with weight 0.4
            for rec in collab_recs:
                course_id = rec['course_id']
                if course_id in combined_recs:
                    # Combine scores
                    combined_recs[course_id]['combined_score'] += rec['similarity_score'] * 0.4
                    combined_recs[course_id]['recommendation_reason'] += ' + Collaborative filtering'
                else:
                    combined_recs[course_id] = {
                        **rec,
                        'combined_score': rec['similarity_score'] * 0.4,
                        'recommendation_reason': 'Collaborative: ' + rec['recommendation_reason']
                    }
            
            # Sort by combined score
            final_recommendations = sorted(
                combined_recs.values(),
                key=lambda x: x['combined_score'],
                reverse=True
            )[:num_recommendations]
            
            return final_recommendations
            
        except Exception as e:
            print(f"Error getting hybrid recommendations: {e}")
            return await self.get_content_based_recommendations(user_enrolled_courses, num_recommendations)

course_recommender = CourseRecommender()
