# 🎓 Learning Management System (LMS)

A comprehensive full-stack Learning Management System built with **FastAPI**, **MongoDB**, **JWT Authentication**, **AI/ML features**, and **Streamlit** frontend.

## 🌟 Features

### 🤖 AI-Powered Features
- **Quiz Generator**: Generate quizzes using Gemini AI based on topics and difficulty
- **Essay Grader**: Automatically grade essays with detailed feedback using AI
- **Score Predictor**: Predict student performance using machine learning models
- **Course Recommender**: Personalized course recommendations using collaborative filtering
- **Performance Summarizer**: Generate insights from student performance data

### 🔐 Role-Based Access Control
- **Admin**: User management, system analytics, performance analysis
- **Professor**: Course creation, quiz generation, essay grading, score prediction
- **Student**: Course enrollment, quiz taking, progress tracking, recommendations

### 📊 Advanced Analytics
- Real-time dashboards for all user roles
- Performance tracking and visualization
- Comprehensive reporting tools
- Progress monitoring

## 🏗️ Architecture

### Backend (FastAPI)
- **FastAPI**: Modern, fast web framework for building APIs
- **MongoDB**: NoSQL database with Motor async driver
- **JWT Authentication**: Secure token-based authentication
- **Pydantic**: Data validation and serialization
- **AI/ML Integration**: Gemini API + scikit-learn

### Frontend (Streamlit)
- **Streamlit**: Python-based web UI framework
- **Role-based dashboards**: Separate interfaces for each user type
- **Interactive charts**: Plotly for data visualization
- **Responsive design**: Modern UI with custom CSS

## 📁 Project Structure

```
LMS/
├── lms-backend/                 # FastAPI Backend
│   ├── main.py                 # FastAPI application entry point
│   ├── auth/                   # Authentication modules
│   │   └── jwt_handler.py      # JWT token handling
│   ├── routes/                 # API route handlers
│   │   ├── auth.py            # Authentication routes
│   │   ├── admin.py           # Admin routes
│   │   ├── professor.py       # Professor routes
│   │   └── student.py         # Student routes
│   ├── models/                 # Pydantic data models
│   │   ├── user.py            # User models
│   │   ├── course.py          # Course models
│   │   └── quiz.py            # Quiz models
│   ├── ml/                     # AI/ML modules
│   │   ├── quiz_generator.py  # AI quiz generation
│   │   ├── essay_grader.py    # AI essay grading
│   │   ├── score_predictor.py # ML score prediction
│   │   ├── recommender.py     # Course recommendation
│   │   └── summarizer.py      # Performance analysis
│   ├── database/              # Database configuration
│   │   ├── connection.py      # MongoDB connection
│   │   └── collections.py     # Collection helpers
│   ├── utils/                 # Utility functions
│   │   └── hashing.py         # Password hashing
│   ├── config/                # Configuration
│   │   └── settings.py        # App settings
│   └── requirements.txt       # Python dependencies
│
├── streamlit-ui/               # Streamlit Frontend
│   ├── main.py                # Main application
│   ├── pages/                 # Streamlit pages
│   │   ├── login.py           # Login page
│   │   ├── register.py        # Registration page
│   │   ├── admin_dashboard.py # Admin dashboard
│   │   ├── professor_dashboard.py # Professor dashboard
│   │   └── student_dashboard.py   # Student dashboard
│   ├── services/              # API client services
│   │   └── api.py             # Backend API client
│   ├── utils/                 # Utility functions
│   │   └── auth.py            # Authentication helpers
│   └── requirements.txt       # Python dependencies
│
└── README.md                  # This file
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MongoDB (local or cloud)
- Gemini API key (optional, for AI features)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd LMS
```

### 2. Backend Setup

```bash
cd lms-backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration
```

### 3. Frontend Setup

```bash
cd streamlit-ui

# Create virtual environment (if not using the same one)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration
```

### 4. Database Setup

Make sure MongoDB is running locally or configure your cloud MongoDB connection in the `.env` file.

### 5. Run the Application

**Terminal 1 - Backend:**
```bash
cd lms-backend
uvicorn main:app --reload --port 8000
```

**Terminal 2 - Frontend:**
```bash
cd streamlit-ui
streamlit run main.py --server.port 8501
```

### 6. Access the Application

- **Frontend**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔧 Configuration

### Environment Variables

**Backend (.env):**
```env
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=lms_db
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
GEMINI_API_KEY=your-gemini-api-key-here
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:8501
```

**Frontend (.env):**
```env
BACKEND_URL=http://localhost:8000
```

## 👥 Default Accounts

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Note**: Create using the "Setup Admin Account" button on the homepage

### Demo Accounts
Create these accounts through the registration page:
- **Professor**: <EMAIL> / prof123
- **Student**: <EMAIL> / student123

## 🤖 AI Features Setup

### Gemini API
1. Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add it to your `.env` file as `GEMINI_API_KEY`
3. AI features will work with the API key, or fall back to mock data without it

### Machine Learning Models
- Score prediction model trains automatically with dummy data
- Models are saved in `lms-backend/ml/models/`
- Retrain with real data as it becomes available

## 📚 API Documentation

The FastAPI backend provides interactive API documentation:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Key Endpoints

**Authentication:**
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/create-admin` - Create admin user

**Admin:**
- `GET /admin/users` - Get all users
- `POST /admin/users` - Create user
- `POST /admin/upload-performance-data` - Upload performance data
- `POST /admin/summarize-performance` - Generate AI summary

**Professor:**
- `POST /professor/create-course` - Create course
- `POST /professor/generate-quiz` - Generate AI quiz
- `POST /professor/grade-essay` - Grade essay with AI
- `POST /professor/predict-score` - Predict student score

**Student:**
- `GET /student/courses` - Get available courses
- `POST /student/enroll/{course_id}` - Enroll in course
- `GET /student/quiz/{quiz_id}` - Get quiz
- `POST /student/quiz/{quiz_id}/submit` - Submit quiz
- `GET /student/recommend` - Get course recommendations

## 🧪 Testing

### Backend Testing
```bash
cd lms-backend
pytest
```

### Manual Testing
1. Start both backend and frontend
2. Create admin account
3. Register as professor and student
4. Test all features through the UI

## 🚀 Deployment

### Backend Deployment
- Deploy FastAPI app to services like Heroku, Railway, or DigitalOcean
- Configure MongoDB Atlas for cloud database
- Set environment variables in production

### Frontend Deployment
- Deploy Streamlit app to Streamlit Cloud or other platforms
- Update `BACKEND_URL` to point to deployed backend

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the code comments and docstrings
- Create an issue in the repository

## 🔮 Future Enhancements

- Real-time notifications
- Video content support
- Advanced analytics dashboard
- Mobile app development
- Integration with external LMS platforms
- Advanced AI features (content generation, plagiarism detection)

---

**Built with ❤️ using FastAPI, MongoDB, Streamlit, and AI**
