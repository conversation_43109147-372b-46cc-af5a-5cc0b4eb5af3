from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class QuestionOption(BaseModel):
    option: str
    is_correct: bool

class Question(BaseModel):
    question: str
    options: List[QuestionOption]
    explanation: Optional[str] = None

class QuizBase(BaseModel):
    title: str
    description: str
    topic: str
    difficulty: str  # easy, medium, hard
    time_limit: int  # in minutes

class QuizCreate(QuizBase):
    course_id: str
    questions: List[Question]

class QuizUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    time_limit: Optional[int] = None
    questions: Optional[List[Question]] = None

class QuizInDB(QuizBase):
    id: str = Field(alias="_id")
    course_id: str
    professor_id: str
    questions: List[Question]
    created_at: datetime
    updated_at: datetime
    is_active: bool = True

class QuizResponse(QuizBase):
    id: str = Field(alias="_id")
    course_id: str
    professor_id: str
    question_count: int
    created_at: datetime

class QuizAttempt(BaseModel):
    quiz_id: str
    answers: List[int]  # List of selected option indices

class QuizResultBase(BaseModel):
    student_id: str
    quiz_id: str
    answers: List[int]
    score: float
    total_questions: int
    correct_answers: int

class QuizResultCreate(QuizResultBase):
    pass

class QuizResultInDB(QuizResultBase):
    id: str = Field(alias="_id")
    submitted_at: datetime
    time_taken: int  # in seconds

class QuizResultResponse(QuizResultBase):
    id: str = Field(alias="_id")
    submitted_at: datetime
    time_taken: int
    percentage: float

class QuizGenerationRequest(BaseModel):
    topic: str
    difficulty: str = "medium"
    num_questions: int = 5

class EssayGradingRequest(BaseModel):
    essay_text: str
    rubric: str
    max_score: int = 100
