from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status
from datetime import datetime
import uuid

from database.collections import get_users_collection
from models.user import User<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Token, UserResponse
from utils.hashing import hash_password, verify_password
from auth.jwt_handler import create_access_token

router = APIRouter(prefix="/auth", tags=["authentication"])

@router.post("/register", response_model=Token)
async def register(user_data: UserCreate):
    """Register a new user"""
    try:
        users_collection = await get_users_collection()
        
        # Check if user already exists
        existing_user = await users_collection.find_one({"email": user_data.email})
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create new user
        user_dict = user_data.dict()
        user_dict["_id"] = str(uuid.uuid4())
        user_dict["hashed_password"] = hash_password(user_dict.pop("password"))
        user_dict["created_at"] = datetime.utcnow()
        user_dict["updated_at"] = datetime.utcnow()
        user_dict["is_active"] = True
        
        # Insert user
        await users_collection.insert_one(user_dict)
        
        # Create access token
        token_data = {
            "sub": user_data.email,
            "role": user_data.role.value
        }
        access_token = create_access_token(token_data)
        
        # Prepare user response
        user_response = UserResponse(
            _id=user_dict["_id"],
            email=user_dict["email"],
            full_name=user_dict["full_name"],
            role=user_dict["role"],
            created_at=user_dict["created_at"],
            is_active=user_dict["is_active"]
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error registering user: {str(e)}"
        )

@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin):
    """Login user"""
    try:
        users_collection = await get_users_collection()
        
        # Find user by email
        user = await users_collection.find_one({"email": user_credentials.email})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Verify password
        if not verify_password(user_credentials.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Check if user is active
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is deactivated"
            )
        
        # Create access token
        token_data = {
            "sub": user["email"],
            "role": user["role"]
        }
        access_token = create_access_token(token_data)
        
        # Prepare user response
        user_response = UserResponse(
            _id=str(user["_id"]),
            email=user["email"],
            full_name=user["full_name"],
            role=user["role"],
            created_at=user["created_at"],
            is_active=user["is_active"]
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error logging in: {str(e)}"
        )

@router.post("/create-admin")
async def create_admin():
    """Create default admin user (for initial setup)"""
    try:
        users_collection = await get_users_collection()
        
        # Check if admin already exists
        existing_admin = await users_collection.find_one({"role": "admin"})
        if existing_admin:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Admin user already exists"
            )
        
        # Create admin user
        admin_data = {
            "_id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "full_name": "System Administrator",
            "role": "admin",
            "hashed_password": hash_password("admin123"),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_active": True
        }
        
        await users_collection.insert_one(admin_data)
        
        return {
            "message": "Admin user created successfully",
            "email": "<EMAIL>",
            "password": "admin123",
            "note": "Please change the password after first login"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating admin: {str(e)}"
        )
