# 🚀 LMS Backend - FastAPI

The backend API server for the Learning Management System, built with FastAPI, MongoDB, and AI/ML integrations.

## 🌟 Features

- **FastAPI Framework**: Modern, fast, and async web framework
- **MongoDB Integration**: NoSQL database with Motor async driver
- **JWT Authentication**: Secure token-based authentication with role-based access
- **AI/ML Features**: Gemini API integration and scikit-learn models
- **Comprehensive API**: RESTful endpoints for all LMS functionality
- **Data Validation**: Pydantic models for request/response validation
- **Auto Documentation**: Interactive API docs with Swagger UI

## 📁 Project Structure

```
lms-backend/
├── main.py                 # FastAPI application entry point
├── auth/                   # Authentication modules
│   └── jwt_handler.py      # JWT token handling and role validation
├── routes/                 # API route handlers
│   ├── auth.py            # Authentication endpoints
│   ├── admin.py           # Admin management endpoints
│   ├── professor.py       # Professor functionality endpoints
│   └── student.py         # Student functionality endpoints
├── models/                 # Pydantic data models
│   ├── user.py            # User-related models
│   ├── course.py          # Course and enrollment models
│   └── quiz.py            # Quiz and result models
├── ml/                     # AI/ML modules
│   ├── quiz_generator.py  # AI-powered quiz generation
│   ├── essay_grader.py    # AI essay grading
│   ├── score_predictor.py # ML score prediction
│   ├── recommender.py     # Course recommendation system
│   └── summarizer.py      # Performance analysis and insights
├── database/              # Database configuration
│   ├── connection.py      # MongoDB connection and initialization
│   └── collections.py     # Database collection helpers
├── utils/                 # Utility functions
│   └── hashing.py         # Password hashing utilities
├── config/                # Configuration management
│   └── settings.py        # Application settings and environment variables
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
└── README.md             # This file
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MongoDB (local or cloud)
- Gemini API key (optional, for AI features)

### Installation

1. **Clone and navigate to backend:**
```bash
cd lms-backend
```

2. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Setup environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run the application:**
```bash
uvicorn main:app --reload --port 8000
```

The API will be available at:
- **API Server**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## ⚙️ Configuration

### Environment Variables (.env)

```env
# Database Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=lms_db

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# AI/ML Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# Application URLs
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:8501
```

### MongoDB Setup

**Local MongoDB:**
```bash
# Install MongoDB locally
# Start MongoDB service
mongod --dbpath /path/to/your/db
```

**MongoDB Atlas (Cloud):**
1. Create account at [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Create cluster and get connection string
3. Update `MONGODB_URL` in `.env`

## 🔐 Authentication & Authorization

### JWT Token System
- Tokens expire after 24 hours (configurable)
- Role-based access control (admin, professor, student)
- Secure password hashing with bcrypt

### User Roles

**Admin:**
- Full system access
- User management
- System analytics
- Performance analysis

**Professor:**
- Course creation and management
- Quiz generation and grading
- Student performance analysis
- AI-powered teaching tools

**Student:**
- Course enrollment
- Quiz participation
- Progress tracking
- Personalized recommendations

## 🤖 AI/ML Features

### 1. Quiz Generator (`ml/quiz_generator.py`)
- Uses Gemini AI to generate quiz questions
- Supports different topics and difficulty levels
- Fallback to mock data if API unavailable

### 2. Essay Grader (`ml/essay_grader.py`)
- AI-powered essay grading with detailed feedback
- Customizable rubrics and scoring criteria
- Provides strengths, improvements, and suggestions

### 3. Score Predictor (`ml/score_predictor.py`)
- Machine learning model for performance prediction
- Uses scikit-learn RandomForestRegressor
- Trains on student performance data

### 4. Course Recommender (`ml/recommender.py`)
- Hybrid recommendation system
- Content-based and collaborative filtering
- Personalized course suggestions

### 5. Performance Summarizer (`ml/summarizer.py`)
- AI-generated insights from performance data
- Comprehensive analysis and recommendations
- Actionable feedback for administrators

## 📚 API Endpoints

### Authentication Endpoints

```
POST /auth/register          # Register new user
POST /auth/login            # User login
POST /auth/create-admin     # Create admin user (setup)
```

### Admin Endpoints

```
GET  /admin/users                    # Get all users
POST /admin/users                    # Create user
PUT  /admin/users/{user_id}          # Update user
DELETE /admin/users/{user_id}        # Delete user
GET  /admin/courses                  # Get all courses
POST /admin/upload-performance-data  # Upload performance data
POST /admin/summarize-performance    # Generate AI summary
GET  /admin/dashboard-stats          # Get dashboard statistics
```

### Professor Endpoints

```
POST /professor/create-course        # Create new course
GET  /professor/courses             # Get professor's courses
PUT  /professor/courses/{course_id} # Update course
POST /professor/generate-quiz       # Generate AI quiz
POST /professor/create-quiz         # Create quiz
GET  /professor/quizzes            # Get professor's quizzes
POST /professor/predict-score      # Predict student score
POST /professor/grade-essay        # Grade essay with AI
GET  /professor/quiz-results/{quiz_id} # Get quiz results
GET  /professor/dashboard-stats    # Get dashboard statistics
```

### Student Endpoints

```
GET  /student/courses              # Get available courses
GET  /student/enrolled-courses     # Get enrolled courses
POST /student/enroll/{course_id}   # Enroll in course
GET  /student/course/{course_id}/quizzes # Get course quizzes
GET  /student/quiz/{quiz_id}       # Get quiz for taking
POST /student/quiz/{quiz_id}/submit # Submit quiz answers
GET  /student/quiz-results         # Get student's quiz results
GET  /student/recommend           # Get course recommendations
GET  /student/dashboard-stats     # Get dashboard statistics
```

## 🗄️ Database Schema

### Collections

**users:**
```json
{
  "_id": "string",
  "email": "string",
  "full_name": "string",
  "role": "admin|professor|student",
  "hashed_password": "string",
  "created_at": "datetime",
  "updated_at": "datetime",
  "is_active": "boolean"
}
```

**courses:**
```json
{
  "_id": "string",
  "title": "string",
  "description": "string",
  "category": "string",
  "difficulty_level": "string",
  "professor_id": "string",
  "professor_name": "string",
  "enrolled_students": ["string"],
  "created_at": "datetime",
  "updated_at": "datetime",
  "is_active": "boolean"
}
```

**quizzes:**
```json
{
  "_id": "string",
  "title": "string",
  "description": "string",
  "topic": "string",
  "difficulty": "string",
  "time_limit": "number",
  "course_id": "string",
  "professor_id": "string",
  "questions": [
    {
      "question": "string",
      "options": [
        {
          "option": "string",
          "is_correct": "boolean"
        }
      ],
      "explanation": "string"
    }
  ],
  "created_at": "datetime",
  "updated_at": "datetime",
  "is_active": "boolean"
}
```

## 🧪 Testing

### Run Tests
```bash
pytest
```

### Manual Testing
1. Start the server: `uvicorn main:app --reload`
2. Visit http://localhost:8000/docs
3. Test endpoints using the interactive documentation

### Test Data
- Use `/auth/create-admin` to create initial admin
- Register test users through `/auth/register`
- Create test courses and quizzes through the API

## 🚀 Deployment

### Production Setup

1. **Environment Configuration:**
```bash
# Set production environment variables
export MONGODB_URL="mongodb+srv://..."
export JWT_SECRET_KEY="production-secret-key"
export GEMINI_API_KEY="your-api-key"
```

2. **Install Production Dependencies:**
```bash
pip install gunicorn
```

3. **Run with Gunicorn:**
```bash
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker Deployment

```dockerfile
FROM python:3.9

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Cloud Deployment Options
- **Heroku**: Easy deployment with git integration
- **Railway**: Modern deployment platform
- **DigitalOcean App Platform**: Scalable container deployment
- **AWS Lambda**: Serverless deployment with Mangum

## 🔧 Development

### Code Structure
- Follow FastAPI best practices
- Use async/await for database operations
- Implement proper error handling
- Add comprehensive logging

### Adding New Features
1. Create Pydantic models in `models/`
2. Add database operations in `database/`
3. Implement routes in `routes/`
4. Add authentication/authorization as needed
5. Update documentation

### AI/ML Integration
- Add new AI features in `ml/` directory
- Implement fallback mechanisms for API failures
- Use background tasks for long-running operations
- Cache results when appropriate

## 📝 Logging

The application uses `loguru` for logging:
- Database connection events
- Authentication attempts
- API request/response logging
- Error tracking and debugging

## 🔒 Security

- Password hashing with bcrypt
- JWT token validation
- Role-based access control
- Input validation with Pydantic
- CORS configuration for frontend integration

## 🤝 Contributing

1. Follow PEP 8 style guidelines
2. Add type hints to all functions
3. Write comprehensive docstrings
4. Add tests for new features
5. Update API documentation

---

**For more information, see the main project README.md**
