from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from typing import List, Dict, Any
import pandas as pd
import io
from datetime import datetime

from auth.jwt_handler import require_admin, TokenData
from database.collections import (
    get_users_collection, get_courses_collection, 
    get_performance_collection, get_quiz_results_collection
)
from models.user import UserResponse, UserCreate, UserUpdate, UserRole
from models.course import CourseResponse
from ml.summarizer import performance_summarizer
from utils.hashing import hash_password

router = APIRouter(prefix="/admin", tags=["admin"])

@router.get("/users", response_model=List[UserResponse])
async def get_all_users(current_user: TokenData = Depends(require_admin)):
    """Get all users in the system"""
    try:
        users_collection = await get_users_collection()
        users_cursor = users_collection.find({}, {"hashed_password": 0})
        users = []
        
        async for user in users_cursor:
            user["_id"] = str(user["_id"])
            users.append(UserResponse(**user))
        
        return users
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching users: {str(e)}"
        )

@router.post("/users", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    current_user: TokenData = Depends(require_admin)
):
    """Create a new user"""
    try:
        users_collection = await get_users_collection()
        
        # Check if user already exists
        existing_user = await users_collection.find_one({"email": user_data.email})
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create new user
        user_dict = user_data.dict()
        user_dict["hashed_password"] = hash_password(user_dict.pop("password"))
        user_dict["created_at"] = datetime.utcnow()
        user_dict["updated_at"] = datetime.utcnow()
        user_dict["is_active"] = True
        
        result = await users_collection.insert_one(user_dict)
        user_dict["_id"] = str(result.inserted_id)
        
        return UserResponse(**user_dict)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating user: {str(e)}"
        )

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: TokenData = Depends(require_admin)
):
    """Update user information"""
    try:
        users_collection = await get_users_collection()
        
        # Prepare update data
        update_data = {k: v for k, v in user_update.dict().items() if v is not None}
        if update_data:
            update_data["updated_at"] = datetime.utcnow()
            
            result = await users_collection.update_one(
                {"_id": user_id},
                {"$set": update_data}
            )
            
            if result.matched_count == 0:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
        
        # Return updated user
        updated_user = await users_collection.find_one(
            {"_id": user_id},
            {"hashed_password": 0}
        )
        updated_user["_id"] = str(updated_user["_id"])
        
        return UserResponse(**updated_user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating user: {str(e)}"
        )

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: str,
    current_user: TokenData = Depends(require_admin)
):
    """Delete a user"""
    try:
        users_collection = await get_users_collection()
        
        result = await users_collection.delete_one({"_id": user_id})
        
        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return {"message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting user: {str(e)}"
        )

@router.get("/courses", response_model=List[CourseResponse])
async def get_all_courses(current_user: TokenData = Depends(require_admin)):
    """Get all courses in the system"""
    try:
        courses_collection = await get_courses_collection()
        courses_cursor = courses_collection.find({})
        courses = []

        async for course in courses_cursor:
            course["_id"] = str(course["_id"])
            course["enrolled_count"] = len(course.get("enrolled_students", []))
            courses.append(CourseResponse(**course))

        return courses
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching courses: {str(e)}"
        )

@router.post("/upload-performance-data")
async def upload_performance_data(
    file: UploadFile = File(...),
    current_user: TokenData = Depends(require_admin)
):
    """Upload student performance data for analysis"""
    try:
        # Validate file type
        if not file.filename.endswith(('.csv', '.xlsx')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only CSV and Excel files are supported"
            )

        # Read file content
        content = await file.read()

        # Parse file based on type
        if file.filename.endswith('.csv'):
            df = pd.read_csv(io.StringIO(content.decode('utf-8')))
        else:
            df = pd.read_excel(io.BytesIO(content))

        # Validate required columns
        required_columns = ['student_id', 'course_id', 'score']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required columns: {missing_columns}"
            )

        # Store performance data
        performance_collection = await get_performance_collection()

        # Convert DataFrame to list of dictionaries
        performance_records = df.to_dict('records')
        for record in performance_records:
            record['uploaded_at'] = datetime.utcnow()
            record['uploaded_by'] = current_user.email

        # Insert data
        await performance_collection.insert_many(performance_records)

        return {
            "message": "Performance data uploaded successfully",
            "records_count": len(performance_records),
            "columns": list(df.columns)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading performance data: {str(e)}"
        )

@router.post("/summarize-performance")
async def summarize_performance(current_user: TokenData = Depends(require_admin)):
    """Generate AI-powered performance summary"""
    try:
        # Get performance data
        performance_collection = await get_performance_collection()
        performance_cursor = performance_collection.find({})
        performance_data = []

        async for record in performance_cursor:
            record["_id"] = str(record["_id"])
            performance_data.append(record)

        if not performance_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No performance data available. Please upload data first."
            )

        # Generate summary using AI
        summary = await performance_summarizer.summarize_performance(performance_data)

        return {
            "summary": summary,
            "data_points": len(performance_data),
            "generated_at": datetime.utcnow().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating performance summary: {str(e)}"
        )

@router.get("/dashboard-stats")
async def get_dashboard_stats(current_user: TokenData = Depends(require_admin)):
    """Get dashboard statistics for admin"""
    try:
        users_collection = await get_users_collection()
        courses_collection = await get_courses_collection()
        quiz_results_collection = await get_quiz_results_collection()

        # Count users by role
        user_stats = {}
        for role in UserRole:
            count = await users_collection.count_documents({"role": role.value})
            user_stats[role.value] = count

        # Course statistics
        total_courses = await courses_collection.count_documents({})
        active_courses = await courses_collection.count_documents({"is_active": True})

        # Quiz statistics
        total_quiz_attempts = await quiz_results_collection.count_documents({})

        # Recent activity (last 7 days)
        from datetime import timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)

        recent_users = await users_collection.count_documents({
            "created_at": {"$gte": week_ago}
        })

        recent_quiz_attempts = await quiz_results_collection.count_documents({
            "submitted_at": {"$gte": week_ago}
        })

        return {
            "user_statistics": user_stats,
            "course_statistics": {
                "total_courses": total_courses,
                "active_courses": active_courses
            },
            "quiz_statistics": {
                "total_attempts": total_quiz_attempts,
                "recent_attempts": recent_quiz_attempts
            },
            "recent_activity": {
                "new_users_this_week": recent_users,
                "quiz_attempts_this_week": recent_quiz_attempts
            },
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard stats: {str(e)}"
        )
