import streamlit as st
from services.api import api_client

# Page configuration
st.set_page_config(
    page_title="Register - LMS",
    page_icon="📝",
    layout="centered"
)

# Custom CSS
st.markdown("""
<style>
    .register-container {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
    }
    
    .register-header {
        text-align: center;
        color: #667eea;
        margin-bottom: 2rem;
    }
    
    .role-info {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #28a745;
        margin-top: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="register-header">
        <h1>📝 Register for LMS</h1>
        <p>Create your learning account</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Check if already logged in
    if "jwt_token" in st.session_state:
        st.success("You are already logged in!")
        if st.button("Go to Dashboard"):
            st.switch_page("main.py")
        return
    
    # Registration form
    with st.container():
        st.markdown('<div class="register-container">', unsafe_allow_html=True)
        
        with st.form("register_form"):
            st.markdown("### Create your account")
            
            # Personal information
            full_name = st.text_input(
                "Full Name",
                placeholder="Enter your full name",
                help="Your display name in the system"
            )
            
            email = st.text_input(
                "Email Address",
                placeholder="Enter your email",
                help="This will be your login username"
            )
            
            password = st.text_input(
                "Password",
                type="password",
                placeholder="Create a password",
                help="Choose a strong password"
            )
            
            confirm_password = st.text_input(
                "Confirm Password",
                type="password",
                placeholder="Confirm your password",
                help="Re-enter your password"
            )
            
            # Role selection
            role = st.selectbox(
                "Select your role",
                options=["student", "professor"],
                help="Choose your role in the system"
            )
            
            # Role information
            if role == "student":
                st.markdown("""
                <div class="role-info">
                    <strong>👨‍🎓 Student Role:</strong><br>
                    • Enroll in courses<br>
                    • Take quizzes and assessments<br>
                    • View grades and progress<br>
                    • Get personalized recommendations
                </div>
                """, unsafe_allow_html=True)
            elif role == "professor":
                st.markdown("""
                <div class="role-info">
                    <strong>👨‍🏫 Professor Role:</strong><br>
                    • Create and manage courses<br>
                    • Generate AI-powered quizzes<br>
                    • Grade essays automatically<br>
                    • Predict student performance
                </div>
                """, unsafe_allow_html=True)
            
            # Terms and conditions
            terms_accepted = st.checkbox(
                "I agree to the Terms of Service and Privacy Policy",
                help="You must accept the terms to create an account"
            )
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                register_button = st.form_submit_button(
                    "📝 Create Account",
                    use_container_width=True,
                    type="primary"
                )
            
            with col2:
                if st.form_submit_button("🔑 Login", use_container_width=True):
                    st.switch_page("pages/login.py")
        
        # Handle registration
        if register_button:
            # Validation
            errors = []
            
            if not full_name:
                errors.append("Full name is required")
            
            if not email:
                errors.append("Email is required")
            elif "@" not in email:
                errors.append("Please enter a valid email address")
            
            if not password:
                errors.append("Password is required")
            elif len(password) < 6:
                errors.append("Password must be at least 6 characters long")
            
            if password != confirm_password:
                errors.append("Passwords do not match")
            
            if not terms_accepted:
                errors.append("You must accept the terms and conditions")
            
            if errors:
                for error in errors:
                    st.error(error)
            else:
                try:
                    with st.spinner("Creating your account..."):
                        user_data = {
                            "full_name": full_name,
                            "email": email,
                            "password": password,
                            "role": role
                        }
                        response = api_client.register(user_data)
                    
                    # Store token and user info in session
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    
                    st.success(f"Account created successfully! Welcome, {full_name}!")
                    st.balloons()
                    
                    # Redirect based on role
                    if role == "professor":
                        st.switch_page("pages/professor_dashboard.py")
                    elif role == "student":
                        st.switch_page("pages/student_dashboard.py")
                    else:
                        st.switch_page("main.py")
                        
                except Exception as e:
                    st.error(f"Registration failed: {str(e)}")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Additional information
    with st.expander("ℹ️ Account Information"):
        st.markdown("""
        ### Account Types
        
        **👨‍🎓 Student Account:**
        - Access to all available courses
        - Take quizzes and track progress
        - Receive personalized course recommendations
        - View detailed performance analytics
        
        **👨‍🏫 Professor Account:**
        - Create and manage courses
        - Use AI to generate quiz questions
        - Automatically grade essays
        - Predict student performance using ML
        - Access detailed class analytics
        
        **👑 Admin Account:**
        - Admin accounts can only be created by existing admins
        - Full system management capabilities
        - User management and system analytics
        
        ### Security
        - All passwords are securely hashed
        - JWT tokens for secure authentication
        - Role-based access control
        """)
    
    # Back to home
    st.markdown("---")
    if st.button("🏠 Back to Home", use_container_width=True):
        st.switch_page("main.py")

if __name__ == "__main__":
    main()
