from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
from datetime import datetime
import uuid

from auth.jwt_handler import require_professor, TokenData
from database.collections import (
    get_courses_collection, get_quizzes_collection, 
    get_quiz_results_collection, get_users_collection
)
from models.course import CourseCreate, CourseResponse, CourseUpdate
from models.quiz import (
    QuizCreate, QuizResponse, QuizGenerationRequest, 
    EssayGradingRequest, QuizResultResponse
)
from ml.quiz_generator import quiz_generator
from ml.score_predictor import score_predictor
from ml.essay_grader import essay_grader

router = APIRouter(prefix="/professor", tags=["professor"])

@router.post("/create-course", response_model=CourseResponse)
async def create_course(
    course_data: CourseCreate,
    current_user: TokenData = Depends(require_professor)
):
    """Create a new course"""
    try:
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()
        
        # Get professor details
        professor = await users_collection.find_one({"email": current_user.email})
        if not professor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Professor not found"
            )
        
        # Create course document
        course_dict = course_data.dict()
        course_dict["_id"] = str(uuid.uuid4())
        course_dict["professor_id"] = str(professor["_id"])
        course_dict["professor_name"] = professor["full_name"]
        course_dict["created_at"] = datetime.utcnow()
        course_dict["updated_at"] = datetime.utcnow()
        course_dict["is_active"] = True
        course_dict["enrolled_students"] = []
        
        # Insert course
        await courses_collection.insert_one(course_dict)
        
        # Prepare response
        course_dict["enrolled_count"] = 0
        return CourseResponse(**course_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating course: {str(e)}"
        )

@router.get("/courses", response_model=List[CourseResponse])
async def get_professor_courses(current_user: TokenData = Depends(require_professor)):
    """Get courses created by the current professor"""
    try:
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()
        
        # Get professor ID
        professor = await users_collection.find_one({"email": current_user.email})
        if not professor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Professor not found"
            )
        
        # Get professor's courses
        courses_cursor = courses_collection.find({"professor_id": str(professor["_id"])})
        courses = []
        
        async for course in courses_cursor:
            course["enrolled_count"] = len(course.get("enrolled_students", []))
            courses.append(CourseResponse(**course))
        
        return courses
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching courses: {str(e)}"
        )

@router.put("/courses/{course_id}", response_model=CourseResponse)
async def update_course(
    course_id: str,
    course_update: CourseUpdate,
    current_user: TokenData = Depends(require_professor)
):
    """Update a course"""
    try:
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()
        
        # Get professor ID
        professor = await users_collection.find_one({"email": current_user.email})
        professor_id = str(professor["_id"])
        
        # Check if course exists and belongs to professor
        course = await courses_collection.find_one({
            "_id": course_id,
            "professor_id": professor_id
        })
        
        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found or access denied"
            )
        
        # Prepare update data
        update_data = {k: v for k, v in course_update.dict().items() if v is not None}
        if update_data:
            update_data["updated_at"] = datetime.utcnow()
            
            await courses_collection.update_one(
                {"_id": course_id},
                {"$set": update_data}
            )
        
        # Return updated course
        updated_course = await courses_collection.find_one({"_id": course_id})
        updated_course["enrolled_count"] = len(updated_course.get("enrolled_students", []))
        
        return CourseResponse(**updated_course)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating course: {str(e)}"
        )

@router.post("/generate-quiz")
async def generate_quiz(
    request: QuizGenerationRequest,
    current_user: TokenData = Depends(require_professor)
):
    """Generate quiz questions using AI"""
    try:
        questions = await quiz_generator.generate_quiz(request)
        
        return {
            "topic": request.topic,
            "difficulty": request.difficulty,
            "num_questions": len(questions),
            "questions": questions,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating quiz: {str(e)}"
        )

@router.post("/create-quiz", response_model=QuizResponse)
async def create_quiz(
    quiz_data: QuizCreate,
    current_user: TokenData = Depends(require_professor)
):
    """Create a new quiz"""
    try:
        quizzes_collection = await get_quizzes_collection()
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()
        
        # Get professor ID
        professor = await users_collection.find_one({"email": current_user.email})
        professor_id = str(professor["_id"])
        
        # Verify course belongs to professor
        course = await courses_collection.find_one({
            "_id": quiz_data.course_id,
            "professor_id": professor_id
        })
        
        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found or access denied"
            )
        
        # Create quiz document
        quiz_dict = quiz_data.dict()
        quiz_dict["_id"] = str(uuid.uuid4())
        quiz_dict["professor_id"] = professor_id
        quiz_dict["created_at"] = datetime.utcnow()
        quiz_dict["updated_at"] = datetime.utcnow()
        quiz_dict["is_active"] = True
        
        # Insert quiz
        await quizzes_collection.insert_one(quiz_dict)
        
        # Prepare response
        quiz_dict["question_count"] = len(quiz_dict["questions"])
        quiz_dict.pop("questions")  # Remove questions from response for brevity
        
        return QuizResponse(**quiz_dict)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating quiz: {str(e)}"
        )

@router.get("/quizzes", response_model=List[QuizResponse])
async def get_professor_quizzes(current_user: TokenData = Depends(require_professor)):
    """Get quizzes created by the current professor"""
    try:
        quizzes_collection = await get_quizzes_collection()
        users_collection = await get_users_collection()

        # Get professor ID
        professor = await users_collection.find_one({"email": current_user.email})
        professor_id = str(professor["_id"])

        # Get professor's quizzes
        quizzes_cursor = quizzes_collection.find({"professor_id": professor_id})
        quizzes = []

        async for quiz in quizzes_cursor:
            quiz["question_count"] = len(quiz.get("questions", []))
            quiz.pop("questions", None)  # Remove questions for brevity
            quizzes.append(QuizResponse(**quiz))

        return quizzes

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching quizzes: {str(e)}"
        )

@router.post("/predict-score")
async def predict_student_score(
    student_data: Dict[str, Any],
    current_user: TokenData = Depends(require_professor)
):
    """Predict student score using ML model"""
    try:
        prediction = await score_predictor.predict_score(student_data)

        return {
            "prediction": prediction,
            "input_data": student_data,
            "predicted_by": current_user.email,
            "prediction_timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error predicting score: {str(e)}"
        )

@router.post("/grade-essay")
async def grade_essay(
    request: EssayGradingRequest,
    current_user: TokenData = Depends(require_professor)
):
    """Grade an essay using AI"""
    try:
        grading_result = await essay_grader.grade_essay(request)

        return {
            "grading_result": grading_result,
            "graded_by": current_user.email,
            "grading_timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error grading essay: {str(e)}"
        )

@router.get("/quiz-results/{quiz_id}")
async def get_quiz_results(
    quiz_id: str,
    current_user: TokenData = Depends(require_professor)
):
    """Get results for a specific quiz"""
    try:
        quizzes_collection = await get_quizzes_collection()
        quiz_results_collection = await get_quiz_results_collection()
        users_collection = await get_users_collection()

        # Get professor ID
        professor = await users_collection.find_one({"email": current_user.email})
        professor_id = str(professor["_id"])

        # Verify quiz belongs to professor
        quiz = await quizzes_collection.find_one({
            "_id": quiz_id,
            "professor_id": professor_id
        })

        if not quiz:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quiz not found or access denied"
            )

        # Get quiz results
        results_cursor = quiz_results_collection.find({"quiz_id": quiz_id})
        results = []

        async for result in results_cursor:
            result["_id"] = str(result["_id"])
            result["percentage"] = (result["correct_answers"] / result["total_questions"]) * 100

            # Get student name
            student = await users_collection.find_one({"_id": result["student_id"]})
            result["student_name"] = student["full_name"] if student else "Unknown"

            results.append(result)

        # Calculate statistics
        if results:
            scores = [r["score"] for r in results]
            stats = {
                "total_attempts": len(results),
                "average_score": sum(scores) / len(scores),
                "highest_score": max(scores),
                "lowest_score": min(scores),
                "pass_rate": len([s for s in scores if s >= 70]) / len(scores) * 100
            }
        else:
            stats = {
                "total_attempts": 0,
                "average_score": 0,
                "highest_score": 0,
                "lowest_score": 0,
                "pass_rate": 0
            }

        return {
            "quiz_title": quiz["title"],
            "quiz_id": quiz_id,
            "results": results,
            "statistics": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching quiz results: {str(e)}"
        )

@router.get("/dashboard-stats")
async def get_professor_dashboard_stats(current_user: TokenData = Depends(require_professor)):
    """Get dashboard statistics for professor"""
    try:
        courses_collection = await get_courses_collection()
        quizzes_collection = await get_quizzes_collection()
        quiz_results_collection = await get_quiz_results_collection()
        users_collection = await get_users_collection()

        # Get professor ID
        professor = await users_collection.find_one({"email": current_user.email})
        professor_id = str(professor["_id"])

        # Course statistics
        total_courses = await courses_collection.count_documents({"professor_id": professor_id})

        # Get enrolled students count
        courses_cursor = courses_collection.find({"professor_id": professor_id})
        total_enrolled = 0
        async for course in courses_cursor:
            total_enrolled += len(course.get("enrolled_students", []))

        # Quiz statistics
        total_quizzes = await quizzes_collection.count_documents({"professor_id": professor_id})

        # Quiz attempts for professor's quizzes
        professor_quizzes = []
        quizzes_cursor = quizzes_collection.find({"professor_id": professor_id})
        async for quiz in quizzes_cursor:
            professor_quizzes.append(quiz["_id"])

        total_quiz_attempts = 0
        if professor_quizzes:
            total_quiz_attempts = await quiz_results_collection.count_documents({
                "quiz_id": {"$in": professor_quizzes}
            })

        return {
            "course_statistics": {
                "total_courses": total_courses,
                "total_enrolled_students": total_enrolled
            },
            "quiz_statistics": {
                "total_quizzes": total_quizzes,
                "total_attempts": total_quiz_attempts
            },
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard stats: {str(e)}"
        )
