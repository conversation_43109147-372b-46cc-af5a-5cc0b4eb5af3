import requests
import streamlit as st
from typing import Dict, Any, Optional, List
import os
from dotenv import load_dotenv

load_dotenv()

class APIClient:
    def __init__(self):
        self.base_url = os.getenv("BACKEND_URL", "http://localhost:8000")
        self.session = requests.Session()
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication token"""
        headers = {"Content-Type": "application/json"}
        
        if "jwt_token" in st.session_state:
            headers["Authorization"] = f"Bearer {st.session_state.jwt_token}"
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """Handle API response"""
        if response.status_code == 401:
            # Clear session on unauthorized
            if "jwt_token" in st.session_state:
                del st.session_state.jwt_token
            if "user" in st.session_state:
                del st.session_state.user
            st.error("Session expired. Please login again.")
            st.rerun()
        
        if not response.ok:
            error_detail = response.json().get("detail", "Unknown error")
            raise Exception(f"API Error: {error_detail}")
        
        return response.json()
    
    # Authentication endpoints
    def login(self, email: str, password: str) -> Dict[str, Any]:
        """Login user"""
        response = self.session.post(
            f"{self.base_url}/auth/login",
            json={"email": email, "password": password}
        )
        return self._handle_response(response)
    
    def register(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register new user"""
        response = self.session.post(
            f"{self.base_url}/auth/register",
            json=user_data
        )
        return self._handle_response(response)
    
    def create_admin(self) -> Dict[str, Any]:
        """Create default admin user"""
        response = self.session.post(f"{self.base_url}/auth/create-admin")
        return self._handle_response(response)
    
    # Admin endpoints
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users (admin only)"""
        response = self.session.get(
            f"{self.base_url}/admin/users",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new user (admin only)"""
        response = self.session.post(
            f"{self.base_url}/admin/users",
            json=user_data,
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def upload_performance_data(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Upload performance data file (admin only)"""
        files = {"file": (filename, file_content)}
        headers = {"Authorization": f"Bearer {st.session_state.jwt_token}"}
        
        response = self.session.post(
            f"{self.base_url}/admin/upload-performance-data",
            files=files,
            headers=headers
        )
        return self._handle_response(response)
    
    def summarize_performance(self) -> Dict[str, Any]:
        """Generate performance summary (admin only)"""
        response = self.session.post(
            f"{self.base_url}/admin/summarize-performance",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_admin_dashboard_stats(self) -> Dict[str, Any]:
        """Get admin dashboard statistics"""
        response = self.session.get(
            f"{self.base_url}/admin/dashboard-stats",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    # Professor endpoints
    def create_course(self, course_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new course (professor only)"""
        response = self.session.post(
            f"{self.base_url}/professor/create-course",
            json=course_data,
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_professor_courses(self) -> List[Dict[str, Any]]:
        """Get professor's courses"""
        response = self.session.get(
            f"{self.base_url}/professor/courses",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def generate_quiz(self, quiz_request: Dict[str, Any]) -> Dict[str, Any]:
        """Generate quiz using AI"""
        response = self.session.post(
            f"{self.base_url}/professor/generate-quiz",
            json=quiz_request,
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def create_quiz(self, quiz_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new quiz"""
        response = self.session.post(
            f"{self.base_url}/professor/create-quiz",
            json=quiz_data,
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def predict_score(self, student_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict student score using ML"""
        response = self.session.post(
            f"{self.base_url}/professor/predict-score",
            json=student_data,
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def grade_essay(self, essay_data: Dict[str, Any]) -> Dict[str, Any]:
        """Grade essay using AI"""
        response = self.session.post(
            f"{self.base_url}/professor/grade-essay",
            json=essay_data,
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_quiz_results(self, quiz_id: str) -> Dict[str, Any]:
        """Get quiz results"""
        response = self.session.get(
            f"{self.base_url}/professor/quiz-results/{quiz_id}",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_professor_dashboard_stats(self) -> Dict[str, Any]:
        """Get professor dashboard statistics"""
        response = self.session.get(
            f"{self.base_url}/professor/dashboard-stats",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    # Student endpoints
    def get_available_courses(self) -> List[Dict[str, Any]]:
        """Get all available courses"""
        response = self.session.get(
            f"{self.base_url}/student/courses",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_enrolled_courses(self) -> List[Dict[str, Any]]:
        """Get student's enrolled courses"""
        response = self.session.get(
            f"{self.base_url}/student/enrolled-courses",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def enroll_in_course(self, course_id: str) -> Dict[str, Any]:
        """Enroll in a course"""
        response = self.session.post(
            f"{self.base_url}/student/enroll/{course_id}",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_course_quizzes(self, course_id: str) -> List[Dict[str, Any]]:
        """Get quizzes for a course"""
        response = self.session.get(
            f"{self.base_url}/student/course/{course_id}/quizzes",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_quiz(self, quiz_id: str) -> Dict[str, Any]:
        """Get quiz for taking"""
        response = self.session.get(
            f"{self.base_url}/student/quiz/{quiz_id}",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def submit_quiz(self, quiz_id: str, answers: List[int]) -> Dict[str, Any]:
        """Submit quiz answers"""
        response = self.session.post(
            f"{self.base_url}/student/quiz/{quiz_id}/submit",
            json={"quiz_id": quiz_id, "answers": answers},
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_quiz_results(self) -> List[Dict[str, Any]]:
        """Get student's quiz results"""
        response = self.session.get(
            f"{self.base_url}/student/quiz-results",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_course_recommendations(self) -> Dict[str, Any]:
        """Get course recommendations"""
        response = self.session.get(
            f"{self.base_url}/student/recommend",
            headers=self._get_headers()
        )
        return self._handle_response(response)
    
    def get_student_dashboard_stats(self) -> Dict[str, Any]:
        """Get student dashboard statistics"""
        response = self.session.get(
            f"{self.base_url}/student/dashboard-stats",
            headers=self._get_headers()
        )
        return self._handle_response(response)

# Global API client instance
api_client = APIClient()
