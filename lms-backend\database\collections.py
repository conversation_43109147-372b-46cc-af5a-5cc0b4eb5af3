from database.connection import get_database

async def get_users_collection():
    db = await get_database()
    return db.users

async def get_courses_collection():
    db = await get_database()
    return db.courses

async def get_quizzes_collection():
    db = await get_database()
    return db.quizzes

async def get_quiz_results_collection():
    db = await get_database()
    return db.quiz_results

async def get_enrollments_collection():
    db = await get_database()
    return db.enrollments

async def get_performance_collection():
    db = await get_database()
    return db.performance_data
