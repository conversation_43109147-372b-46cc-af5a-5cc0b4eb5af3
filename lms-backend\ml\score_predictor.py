import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import os
from typing import Dict, List, Any
from datetime import datetime

class ScorePredictor:
    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_columns = [
            'previous_quiz_scores', 'study_hours', 'attendance_rate',
            'assignment_completion', 'participation_score', 'course_difficulty'
        ]
        self.model_path = "ml/models/score_predictor.pkl"
        self.scaler_path = "ml/models/score_scaler.pkl"
        
        # Create models directory if it doesn't exist
        os.makedirs("ml/models", exist_ok=True)
        
        # Load existing model or create new one
        self._load_or_create_model()
    
    def _load_or_create_model(self):
        """Load existing model or create and train a new one"""
        if os.path.exists(self.model_path) and os.path.exists(self.scaler_path):
            try:
                self.model = joblib.load(self.model_path)
                self.scaler = joblib.load(self.scaler_path)
                print("Loaded existing score prediction model")
            except Exception as e:
                print(f"Error loading model: {e}")
                self._create_and_train_model()
        else:
            self._create_and_train_model()
    
    def _create_and_train_model(self):
        """Create and train a new model with dummy data"""
        print("Creating and training new score prediction model...")
        
        # Generate dummy training data
        dummy_data = self._generate_dummy_data(1000)
        
        # Prepare features and target
        X = dummy_data[self.feature_columns]
        y = dummy_data['final_score']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model = RandomForestRegressor(
            n_estimators=100,
            random_state=42,
            max_depth=10
        )
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        y_pred = self.model.predict(X_test_scaled)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"Model trained - MSE: {mse:.2f}, R2: {r2:.2f}")
        
        # Save model
        joblib.dump(self.model, self.model_path)
        joblib.dump(self.scaler, self.scaler_path)
    
    def _generate_dummy_data(self, n_samples: int) -> pd.DataFrame:
        """Generate dummy student performance data"""
        np.random.seed(42)
        
        data = {
            'previous_quiz_scores': np.random.normal(75, 15, n_samples),
            'study_hours': np.random.exponential(10, n_samples),
            'attendance_rate': np.random.beta(8, 2, n_samples) * 100,
            'assignment_completion': np.random.beta(7, 2, n_samples) * 100,
            'participation_score': np.random.normal(80, 12, n_samples),
            'course_difficulty': np.random.choice([1, 2, 3, 4, 5], n_samples)
        }
        
        # Create realistic final scores based on features
        final_scores = (
            0.3 * data['previous_quiz_scores'] +
            0.2 * np.minimum(data['study_hours'] * 2, 100) +
            0.2 * data['attendance_rate'] +
            0.15 * data['assignment_completion'] +
            0.1 * data['participation_score'] -
            0.05 * data['course_difficulty'] * 10 +
            np.random.normal(0, 5, n_samples)
        )
        
        data['final_score'] = np.clip(final_scores, 0, 100)
        
        return pd.DataFrame(data)
    
    async def predict_score(self, student_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict student score based on performance data"""
        try:
            # Prepare input data
            input_data = pd.DataFrame([student_data])
            
            # Ensure all required columns are present
            for col in self.feature_columns:
                if col not in input_data.columns:
                    input_data[col] = 0  # Default value
            
            # Select and order features
            X = input_data[self.feature_columns]
            
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            # Make prediction
            predicted_score = self.model.predict(X_scaled)[0]
            predicted_score = max(0, min(100, predicted_score))  # Clamp to 0-100
            
            # Get feature importance for explanation
            feature_importance = dict(zip(
                self.feature_columns,
                self.model.feature_importances_
            ))
            
            return {
                "predicted_score": round(predicted_score, 2),
                "confidence": "medium",  # Could be calculated based on model uncertainty
                "feature_importance": feature_importance,
                "prediction_date": datetime.now().isoformat(),
                "model_version": "1.0"
            }
            
        except Exception as e:
            print(f"Error predicting score: {e}")
            return {
                "predicted_score": 75.0,  # Default prediction
                "confidence": "low",
                "error": str(e),
                "prediction_date": datetime.now().isoformat()
            }
    
    def retrain_model(self, new_data: pd.DataFrame):
        """Retrain model with new data"""
        try:
            # Combine with existing dummy data for better performance
            dummy_data = self._generate_dummy_data(500)
            combined_data = pd.concat([dummy_data, new_data], ignore_index=True)
            
            # Prepare features and target
            X = combined_data[self.feature_columns]
            y = combined_data['final_score']
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Retrain model
            self.model.fit(X_scaled, y)
            
            # Save updated model
            joblib.dump(self.model, self.model_path)
            joblib.dump(self.scaler, self.scaler_path)
            
            print("Model retrained successfully")
            
        except Exception as e:
            print(f"Error retraining model: {e}")

score_predictor = ScorePredictor()
