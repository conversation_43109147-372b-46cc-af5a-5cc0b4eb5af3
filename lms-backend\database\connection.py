from motor.motor_asyncio import AsyncIOMotorClient
from config.settings import settings
import asyncio

class Database:
    client: AsyncIOMotorClient = None
    database = None

db = Database()

async def get_database():
    return db.database

async def connect_to_mongo():
    """Create database connection"""
    db.client = AsyncIOMotorClient(settings.MONGODB_URL)
    db.database = db.client[settings.DATABASE_NAME]
    print(f"Connected to MongoDB at {settings.MONGODB_URL}")

async def close_mongo_connection():
    """Close database connection"""
    if db.client:
        db.client.close()
        print("Disconnected from MongoDB")

async def create_indexes():
    """Create database indexes for better performance"""
    try:
        # User collection indexes
        await db.database.users.create_index("email", unique=True)
        await db.database.users.create_index("role")
        
        # Course collection indexes
        await db.database.courses.create_index("professor_id")
        await db.database.courses.create_index("title")
        
        # Quiz collection indexes
        await db.database.quizzes.create_index("course_id")
        await db.database.quizzes.create_index("professor_id")
        
        # Quiz results indexes
        await db.database.quiz_results.create_index("student_id")
        await db.database.quiz_results.create_index("quiz_id")
        
        print("Database indexes created successfully")
    except Exception as e:
        print(f"Error creating indexes: {e}")

# Initialize database connection
async def init_db():
    await connect_to_mongo()
    await create_indexes()
