from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
from datetime import datetime
import uuid

from auth.jwt_handler import require_student, TokenData
from database.collections import (
    get_courses_collection, get_quizzes_collection, 
    get_quiz_results_collection, get_users_collection,
    get_enrollments_collection
)
from models.course import CourseResponse, EnrollmentCreate
from models.quiz import QuizAttempt, QuizResultResponse, QuizInDB
from ml.recommender import course_recommender

router = APIRouter(prefix="/student", tags=["student"])

@router.get("/courses", response_model=List[CourseResponse])
async def get_available_courses(current_user: TokenData = Depends(require_student)):
    """Get all available courses"""
    try:
        courses_collection = await get_courses_collection()
        courses_cursor = courses_collection.find({"is_active": True})
        courses = []
        
        async for course in courses_cursor:
            course["enrolled_count"] = len(course.get("enrolled_students", []))
            courses.append(CourseResponse(**course))
        
        return courses
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching courses: {str(e)}"
        )

@router.get("/enrolled-courses", response_model=List[CourseResponse])
async def get_enrolled_courses(current_user: TokenData = Depends(require_student)):
    """Get courses the student is enrolled in"""
    try:
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()
        
        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        if not student:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Student not found"
            )
        
        student_id = str(student["_id"])
        
        # Get enrolled courses
        courses_cursor = courses_collection.find({
            "enrolled_students": student_id,
            "is_active": True
        })
        courses = []
        
        async for course in courses_cursor:
            course["enrolled_count"] = len(course.get("enrolled_students", []))
            courses.append(CourseResponse(**course))
        
        return courses
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching enrolled courses: {str(e)}"
        )

@router.post("/enroll/{course_id}")
async def enroll_in_course(
    course_id: str,
    current_user: TokenData = Depends(require_student)
):
    """Enroll in a course"""
    try:
        courses_collection = await get_courses_collection()
        enrollments_collection = await get_enrollments_collection()
        users_collection = await get_users_collection()
        
        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])
        
        # Check if course exists
        course = await courses_collection.find_one({"_id": course_id, "is_active": True})
        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )
        
        # Check if already enrolled
        if student_id in course.get("enrolled_students", []):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Already enrolled in this course"
            )
        
        # Add student to course
        await courses_collection.update_one(
            {"_id": course_id},
            {"$push": {"enrolled_students": student_id}}
        )
        
        # Create enrollment record
        enrollment_data = {
            "_id": str(uuid.uuid4()),
            "student_id": student_id,
            "course_id": course_id,
            "enrolled_at": datetime.utcnow(),
            "progress": 0.0,
            "is_active": True
        }
        
        await enrollments_collection.insert_one(enrollment_data)
        
        return {"message": "Successfully enrolled in course"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error enrolling in course: {str(e)}"
        )

@router.get("/course/{course_id}/quizzes")
async def get_course_quizzes(
    course_id: str,
    current_user: TokenData = Depends(require_student)
):
    """Get quizzes for a specific course"""
    try:
        courses_collection = await get_courses_collection()
        quizzes_collection = await get_quizzes_collection()
        users_collection = await get_users_collection()
        
        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])
        
        # Check if student is enrolled in course
        course = await courses_collection.find_one({"_id": course_id})
        if not course or student_id not in course.get("enrolled_students", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enrolled in this course"
            )
        
        # Get course quizzes
        quizzes_cursor = quizzes_collection.find({
            "course_id": course_id,
            "is_active": True
        })
        quizzes = []
        
        async for quiz in quizzes_cursor:
            quiz_data = {
                "id": quiz["_id"],
                "title": quiz["title"],
                "description": quiz["description"],
                "topic": quiz["topic"],
                "difficulty": quiz["difficulty"],
                "time_limit": quiz["time_limit"],
                "question_count": len(quiz.get("questions", [])),
                "created_at": quiz["created_at"]
            }
            quizzes.append(quiz_data)
        
        return quizzes
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching course quizzes: {str(e)}"
        )

@router.get("/quiz/{quiz_id}")
async def get_quiz(
    quiz_id: str,
    current_user: TokenData = Depends(require_student)
):
    """Get quiz questions for taking the quiz"""
    try:
        quizzes_collection = await get_quizzes_collection()
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()
        
        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])
        
        # Get quiz
        quiz = await quizzes_collection.find_one({"_id": quiz_id, "is_active": True})
        if not quiz:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quiz not found"
            )
        
        # Check if student is enrolled in the course
        course = await courses_collection.find_one({"_id": quiz["course_id"]})
        if not course or student_id not in course.get("enrolled_students", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enrolled in this course"
            )
        
        # Prepare quiz for student (remove correct answers)
        questions_for_student = []
        for question in quiz["questions"]:
            student_question = {
                "question": question["question"],
                "options": [opt["option"] for opt in question["options"]]
            }
            questions_for_student.append(student_question)
        
        return {
            "id": quiz["_id"],
            "title": quiz["title"],
            "description": quiz["description"],
            "topic": quiz["topic"],
            "difficulty": quiz["difficulty"],
            "time_limit": quiz["time_limit"],
            "questions": questions_for_student
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching quiz: {str(e)}"
        )

@router.post("/quiz/{quiz_id}/submit")
async def submit_quiz(
    quiz_id: str,
    quiz_attempt: QuizAttempt,
    current_user: TokenData = Depends(require_student)
):
    """Submit quiz answers"""
    try:
        quizzes_collection = await get_quizzes_collection()
        quiz_results_collection = await get_quiz_results_collection()
        courses_collection = await get_courses_collection()
        users_collection = await get_users_collection()

        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])

        # Get quiz
        quiz = await quizzes_collection.find_one({"_id": quiz_id, "is_active": True})
        if not quiz:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quiz not found"
            )

        # Check if student is enrolled in the course
        course = await courses_collection.find_one({"_id": quiz["course_id"]})
        if not course or student_id not in course.get("enrolled_students", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enrolled in this course"
            )

        # Check if already submitted
        existing_result = await quiz_results_collection.find_one({
            "student_id": student_id,
            "quiz_id": quiz_id
        })

        if existing_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Quiz already submitted"
            )

        # Calculate score
        questions = quiz["questions"]
        total_questions = len(questions)
        correct_answers = 0

        for i, answer_index in enumerate(quiz_attempt.answers):
            if i < len(questions) and answer_index < len(questions[i]["options"]):
                if questions[i]["options"][answer_index]["is_correct"]:
                    correct_answers += 1

        score = (correct_answers / total_questions) * 100

        # Create quiz result
        result_data = {
            "_id": str(uuid.uuid4()),
            "student_id": student_id,
            "quiz_id": quiz_id,
            "answers": quiz_attempt.answers,
            "score": score,
            "total_questions": total_questions,
            "correct_answers": correct_answers,
            "submitted_at": datetime.utcnow(),
            "time_taken": 0  # Could be calculated if we track start time
        }

        await quiz_results_collection.insert_one(result_data)

        # Prepare response
        result_data["percentage"] = score
        return QuizResultResponse(**result_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error submitting quiz: {str(e)}"
        )

@router.get("/quiz-results", response_model=List[QuizResultResponse])
async def get_student_quiz_results(current_user: TokenData = Depends(require_student)):
    """Get all quiz results for the student"""
    try:
        quiz_results_collection = await get_quiz_results_collection()
        quizzes_collection = await get_quizzes_collection()
        users_collection = await get_users_collection()

        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])

        # Get student's quiz results
        results_cursor = quiz_results_collection.find({"student_id": student_id})
        results = []

        async for result in results_cursor:
            result["_id"] = str(result["_id"])
            result["percentage"] = (result["correct_answers"] / result["total_questions"]) * 100

            # Get quiz title
            quiz = await quizzes_collection.find_one({"_id": result["quiz_id"]})
            result["quiz_title"] = quiz["title"] if quiz else "Unknown Quiz"

            results.append(QuizResultResponse(**result))

        return results

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching quiz results: {str(e)}"
        )

@router.get("/recommend")
async def get_course_recommendations(current_user: TokenData = Depends(require_student)):
    """Get personalized course recommendations"""
    try:
        courses_collection = await get_courses_collection()
        enrollments_collection = await get_enrollments_collection()
        users_collection = await get_users_collection()

        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])

        # Get all courses for recommender initialization
        courses_cursor = courses_collection.find({"is_active": True})
        courses_data = []
        async for course in courses_cursor:
            course["id"] = course["_id"]
            courses_data.append(course)

        # Initialize recommender with courses
        await course_recommender.initialize_with_courses(courses_data)

        # Get student's enrolled courses
        enrolled_courses_cursor = courses_collection.find({
            "enrolled_students": student_id,
            "is_active": True
        })
        enrolled_course_ids = []
        async for course in enrolled_courses_cursor:
            enrolled_course_ids.append(course["_id"])

        # Get all user enrollments for collaborative filtering
        all_enrollments = {}
        all_users_cursor = users_collection.find({"role": "student"})
        async for user in all_users_cursor:
            user_id = str(user["_id"])
            user_courses_cursor = courses_collection.find({
                "enrolled_students": user_id,
                "is_active": True
            })
            user_courses = []
            async for course in user_courses_cursor:
                user_courses.append(course["_id"])
            all_enrollments[user_id] = user_courses

        # Get hybrid recommendations
        recommendations = await course_recommender.get_hybrid_recommendations(
            student_id,
            enrolled_course_ids,
            all_enrollments,
            num_recommendations=5
        )

        return {
            "recommendations": recommendations,
            "enrolled_courses_count": len(enrolled_course_ids),
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting recommendations: {str(e)}"
        )

@router.get("/dashboard-stats")
async def get_student_dashboard_stats(current_user: TokenData = Depends(require_student)):
    """Get dashboard statistics for student"""
    try:
        courses_collection = await get_courses_collection()
        quiz_results_collection = await get_quiz_results_collection()
        users_collection = await get_users_collection()

        # Get student ID
        student = await users_collection.find_one({"email": current_user.email})
        student_id = str(student["_id"])

        # Enrolled courses count
        enrolled_courses = await courses_collection.count_documents({
            "enrolled_students": student_id,
            "is_active": True
        })

        # Quiz statistics
        quiz_results_cursor = quiz_results_collection.find({"student_id": student_id})
        quiz_results = []
        async for result in quiz_results_cursor:
            quiz_results.append(result)

        total_quizzes = len(quiz_results)
        if total_quizzes > 0:
            scores = [result["score"] for result in quiz_results]
            average_score = sum(scores) / len(scores)
            highest_score = max(scores)
            recent_scores = scores[-5:] if len(scores) >= 5 else scores
        else:
            average_score = 0
            highest_score = 0
            recent_scores = []

        return {
            "enrollment_statistics": {
                "enrolled_courses": enrolled_courses
            },
            "quiz_statistics": {
                "total_quizzes_taken": total_quizzes,
                "average_score": round(average_score, 2),
                "highest_score": round(highest_score, 2),
                "recent_scores": recent_scores
            },
            "generated_at": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard stats: {str(e)}"
        )
