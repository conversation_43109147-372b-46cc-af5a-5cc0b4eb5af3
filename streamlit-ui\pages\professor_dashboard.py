import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import pandas as pd

from utils.auth import require_role
from services.api import api_client

# Page configuration
st.set_page_config(
    page_title="Professor Dashboard - LMS",
    page_icon="👨‍🏫",
    layout="wide"
)

# Require professor role
require_role("professor")

# Custom CSS
st.markdown("""
<style>
    .professor-header {
        background: linear-gradient(90deg, #6f42c1 0%, #e83e8c 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        padding: 1.5rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .feature-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
    
    .course-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid #e1e5e9;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    user = st.session_state.get("user", {})
    st.markdown(f"""
    <div class="professor-header">
        <h1>👨‍🏫 Welcome, Professor {user.get('full_name', 'User')}!</h1>
        <p>Teaching Dashboard & AI Tools</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Load dashboard data
    try:
        with st.spinner("Loading your dashboard..."):
            stats = api_client.get_professor_dashboard_stats()
            courses = api_client.get_professor_courses()
    except Exception as e:
        st.error(f"Error loading dashboard data: {e}")
        return
    
    # Key Metrics
    st.markdown("### 📊 Teaching Overview")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_courses = stats["course_statistics"]["total_courses"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_courses}</div>
            <div style="font-size: 1rem; opacity: 0.9;">My Courses</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        total_students = stats["course_statistics"]["total_enrolled_students"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_students}</div>
            <div style="font-size: 1rem; opacity: 0.9;">Total Students</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        total_quizzes = stats["quiz_statistics"]["total_quizzes"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_quizzes}</div>
            <div style="font-size: 1rem; opacity: 0.9;">Quizzes Created</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        total_attempts = stats["quiz_statistics"]["total_attempts"]
        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_attempts}</div>
            <div style="font-size: 1rem; opacity: 0.9;">Quiz Attempts</div>
        </div>
        """, unsafe_allow_html=True)
    
    # AI Tools Section
    st.markdown("### 🤖 AI-Powered Teaching Tools")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h4>🧠 Quiz Generator</h4>
            <p>Generate quizzes using AI based on topics and difficulty</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("Generate Quiz", use_container_width=True, key="quiz_gen"):
            st.switch_page("pages/professor_quiz_generator.py")
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h4>📝 Essay Grader</h4>
            <p>Automatically grade essays with detailed feedback</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("Grade Essays", use_container_width=True, key="essay_grade"):
            st.switch_page("pages/professor_essay_grader.py")
    
    with col3:
        st.markdown("""
        <div class="feature-card">
            <h4>🔮 Score Predictor</h4>
            <p>Predict student performance using ML models</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("Predict Scores", use_container_width=True, key="score_pred"):
            st.switch_page("pages/professor_score_predictor.py")
    
    with col4:
        st.markdown("""
        <div class="feature-card">
            <h4>📚 Course Manager</h4>
            <p>Create and manage your courses</p>
        </div>
        """, unsafe_allow_html=True)
        if st.button("Manage Courses", use_container_width=True, key="course_mgmt"):
            st.switch_page("pages/professor_courses.py")
    
    # My Courses Section
    st.markdown("### 📚 My Courses")
    
    if courses:
        col1, col2 = st.columns(2)
        
        for i, course in enumerate(courses):
            with col1 if i % 2 == 0 else col2:
                st.markdown(f"""
                <div class="course-card">
                    <h4>{course['title']}</h4>
                    <p><strong>Category:</strong> {course['category']}</p>
                    <p><strong>Difficulty:</strong> {course['difficulty_level'].title()}</p>
                    <p><strong>Enrolled Students:</strong> {course['enrolled_count']}</p>
                    <p>{course['description'][:100]}...</p>
                </div>
                """, unsafe_allow_html=True)
                
                col_quiz, col_results = st.columns(2)
                with col_quiz:
                    if st.button(f"➕ Add Quiz", key=f"add_quiz_{course['id']}", use_container_width=True):
                        st.session_state.selected_course = course
                        st.switch_page("pages/professor_create_quiz.py")
                
                with col_results:
                    if st.button(f"📊 View Results", key=f"results_{course['id']}", use_container_width=True):
                        st.session_state.selected_course = course
                        st.switch_page("pages/professor_quiz_results.py")
    else:
        st.info("You haven't created any courses yet.")
        if st.button("➕ Create Your First Course", use_container_width=True, type="primary"):
            st.switch_page("pages/professor_create_course.py")
    
    # Quick Demo Section
    st.markdown("### 🚀 Quick Demo")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🧠 Try AI Quiz Generation")
        
        with st.form("quick_quiz_demo"):
            topic = st.text_input("Enter a topic:", placeholder="e.g., Python Programming")
            difficulty = st.selectbox("Difficulty:", ["easy", "medium", "hard"])
            
            if st.form_submit_button("Generate Sample Quiz", use_container_width=True):
                if topic:
                    try:
                        with st.spinner("Generating quiz..."):
                            result = api_client.generate_quiz({
                                "topic": topic,
                                "difficulty": difficulty,
                                "num_questions": 3
                            })
                        
                        st.success("Quiz generated successfully!")
                        
                        for i, question in enumerate(result["questions"], 1):
                            st.markdown(f"**Question {i}:** {question['question']}")
                            for j, option in enumerate(question['options']):
                                prefix = "✅" if option['is_correct'] else "❌"
                                st.markdown(f"{prefix} {option['option']}")
                            if question.get('explanation'):
                                st.markdown(f"*Explanation: {question['explanation']}*")
                            st.markdown("---")
                            
                    except Exception as e:
                        st.error(f"Error generating quiz: {e}")
                else:
                    st.error("Please enter a topic")
    
    with col2:
        st.markdown("#### 📝 Try Essay Grading")
        
        with st.form("quick_essay_demo"):
            essay_text = st.text_area(
                "Sample essay to grade:",
                placeholder="Paste an essay here to see AI grading in action...",
                height=150
            )
            rubric = st.text_area(
                "Grading rubric:",
                value="Grade based on content quality, organization, grammar, and creativity. Maximum score: 100 points.",
                height=80
            )
            
            if st.form_submit_button("Grade Essay", use_container_width=True):
                if essay_text:
                    try:
                        with st.spinner("Grading essay..."):
                            result = api_client.grade_essay({
                                "essay_text": essay_text,
                                "rubric": rubric,
                                "max_score": 100
                            })
                        
                        grading = result["grading_result"]
                        
                        st.success(f"Essay graded! Score: {grading['overall_score']}/{grading['max_score']} ({grading['percentage']:.1f}%)")
                        
                        col_strengths, col_improvements = st.columns(2)
                        
                        with col_strengths:
                            st.markdown("**Strengths:**")
                            for strength in grading['strengths']:
                                st.markdown(f"• {strength}")
                        
                        with col_improvements:
                            st.markdown("**Areas for Improvement:**")
                            for improvement in grading['areas_for_improvement']:
                                st.markdown(f"• {improvement}")
                        
                        st.markdown(f"**Detailed Feedback:** {grading['detailed_feedback']}")
                        
                    except Exception as e:
                        st.error(f"Error grading essay: {e}")
                else:
                    st.error("Please enter an essay to grade")
    
    # Navigation
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🏠 Home", use_container_width=True):
            st.switch_page("main.py")
    
    with col2:
        if st.button("📚 Courses", use_container_width=True):
            st.switch_page("pages/professor_courses.py")
    
    with col3:
        if st.button("🧠 AI Tools", use_container_width=True):
            st.switch_page("pages/professor_quiz_generator.py")
    
    with col4:
        if st.button("📊 Analytics", use_container_width=True):
            st.switch_page("pages/professor_analytics.py")

if __name__ == "__main__":
    main()
