# 🎨 LMS Frontend - Streamlit

The frontend web application for the Learning Management System, built with Streamlit and featuring role-based dashboards.

## 🌟 Features

- **Role-Based Dashboards**: Separate interfaces for Admin, Professor, and Student
- **Interactive UI**: Modern, responsive design with custom CSS
- **Real-Time Data**: Live updates from the FastAPI backend
- **Data Visualization**: Charts and graphs using Plotly
- **Authentication Flow**: Secure login/logout with JWT tokens
- **AI Integration**: Direct access to AI-powered features

## 📁 Project Structure

```
streamlit-ui/
├── main.py                    # Main application entry point
├── pages/                     # Streamlit pages
│   ├── login.py              # User login page
│   ├── register.py           # User registration page
│   ├── admin_dashboard.py    # Admin dashboard
│   ├── admin_users.py        # User management (admin)
│   ├── admin_performance.py  # Performance analysis (admin)
│   ├── professor_dashboard.py # Professor dashboard
│   ├── professor_courses.py   # Course management (professor)
│   ├── professor_quiz_generator.py # AI quiz generation
│   ├── professor_essay_grader.py   # AI essay grading
│   ├── professor_score_predictor.py # ML score prediction
│   ├── student_dashboard.py   # Student dashboard
│   ├── student_courses.py     # Course browsing (student)
│   ├── student_quiz.py        # Quiz taking interface
│   ├── student_results.py     # Quiz results and progress
│   └── student_recommendations.py # Course recommendations
├── services/                  # Backend API integration
│   └── api.py                # API client for backend communication
├── utils/                     # Utility functions
│   └── auth.py               # Authentication helpers
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
└── README.md                # This file
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Running LMS Backend (FastAPI server)

### Installation

1. **Navigate to frontend directory:**
```bash
cd streamlit-ui
```

2. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Setup environment variables:**
```bash
cp .env.example .env
# Edit .env with your backend URL
```

5. **Run the application:**
```bash
streamlit run main.py --server.port 8501
```

The application will be available at: http://localhost:8501

## ⚙️ Configuration

### Environment Variables (.env)

```env
# Backend API Configuration
BACKEND_URL=http://localhost:8000
```

### Streamlit Configuration

Create `.streamlit/config.toml` for custom Streamlit settings:

```toml
[server]
port = 8501
headless = true

[theme]
primaryColor = "#667eea"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
```

## 🎨 User Interface

### Design System

**Color Palette:**
- Primary: `#667eea` (Purple-blue gradient)
- Secondary: `#764ba2` (Purple)
- Success: `#28a745` (Green)
- Warning: `#ffc107` (Yellow)
- Danger: `#dc3545` (Red)

**Components:**
- Metric cards with gradient backgrounds
- Hover effects on interactive elements
- Responsive grid layouts
- Custom CSS for enhanced styling

### Page Layouts

**Dashboard Pages:**
- Header with role-specific branding
- Key metrics in card format
- Quick action buttons
- Data visualization charts
- Navigation sidebar

**Form Pages:**
- Centered form containers
- Input validation and feedback
- Progress indicators
- Clear call-to-action buttons

## 👥 Role-Based Features

### 👑 Admin Dashboard
- **User Management**: Create, edit, delete users
- **System Analytics**: User distribution, course statistics
- **Performance Analysis**: Upload data, generate AI insights
- **Course Overview**: View all courses in the system

### 👨‍🏫 Professor Dashboard
- **Course Management**: Create and manage courses
- **AI Quiz Generator**: Generate quizzes using Gemini AI
- **Essay Grader**: Automatically grade essays with feedback
- **Score Predictor**: Predict student performance using ML
- **Analytics**: View student progress and quiz results

### 👨‍🎓 Student Dashboard
- **Course Enrollment**: Browse and enroll in courses
- **Quiz Taking**: Interactive quiz interface
- **Progress Tracking**: View grades and performance
- **Recommendations**: Get personalized course suggestions
- **Results Analysis**: Detailed quiz results and feedback

## 🔐 Authentication System

### Session Management
- JWT tokens stored in `st.session_state`
- Automatic token validation
- Role-based page access control
- Secure logout functionality

### Authentication Flow
1. User visits login page
2. Credentials validated against backend
3. JWT token and user info stored in session
4. User redirected to role-specific dashboard
5. Token included in all API requests

### Access Control
```python
from utils.auth import require_role

# Require specific role
require_role("admin")  # Only admins can access

# Check authentication
from utils.auth import is_authenticated
if not is_authenticated():
    st.switch_page("pages/login.py")
```

## 📊 Data Visualization

### Chart Types
- **Pie Charts**: User distribution, performance categories
- **Bar Charts**: Course statistics, quiz results
- **Line Charts**: Performance trends, progress tracking
- **Metrics**: Key performance indicators

### Plotly Integration
```python
import plotly.express as px
import plotly.graph_objects as go

# Create interactive charts
fig = px.pie(values=data, names=labels, title="Distribution")
st.plotly_chart(fig, use_container_width=True)
```

## 🔌 API Integration

### API Client (`services/api.py`)

The API client handles all backend communication:

```python
from services.api import api_client

# Authentication
response = api_client.login(email, password)

# Get data
courses = api_client.get_enrolled_courses()

# Submit data
result = api_client.submit_quiz(quiz_id, answers)
```

### Error Handling
- Automatic token refresh on 401 errors
- User-friendly error messages
- Fallback for network issues
- Loading states and spinners

## 🎯 Key Features

### AI-Powered Tools
- **Quiz Generation**: Real-time AI quiz creation
- **Essay Grading**: Instant feedback and scoring
- **Performance Prediction**: ML-based score forecasting
- **Course Recommendations**: Personalized suggestions

### Interactive Elements
- **Form Validation**: Real-time input validation
- **Progress Bars**: Visual progress indicators
- **Modal Dialogs**: Confirmation and detail views
- **Responsive Tables**: Sortable and filterable data

### User Experience
- **Navigation**: Intuitive sidebar navigation
- **Breadcrumbs**: Clear page hierarchy
- **Search**: Quick content discovery
- **Filters**: Data filtering and sorting

## 🧪 Testing

### Manual Testing
1. Start the backend server
2. Run the Streamlit app
3. Test all user flows:
   - Registration and login
   - Role-specific features
   - API interactions
   - Error handling

### Test Scenarios
- **Authentication**: Login/logout, role switching
- **Data Flow**: Create, read, update operations
- **AI Features**: Quiz generation, essay grading
- **Error Handling**: Network issues, invalid data

## 🚀 Deployment

### Streamlit Cloud
1. Push code to GitHub repository
2. Connect to Streamlit Cloud
3. Configure environment variables
4. Deploy with automatic updates

### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8501

CMD ["streamlit", "run", "main.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

### Environment Setup
```bash
# Production environment variables
export BACKEND_URL="https://your-backend-api.com"
```

## 🎨 Customization

### Custom CSS
Add custom styles in page files:

```python
st.markdown("""
<style>
    .custom-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)
```

### Theme Configuration
Modify `.streamlit/config.toml` for global theme changes.

### Component Library
Create reusable components:

```python
def create_metric_card(title, value, delta=None):
    return f"""
    <div class="metric-card">
        <h3>{title}</h3>
        <div class="metric-value">{value}</div>
        {f'<div class="metric-delta">{delta}</div>' if delta else ''}
    </div>
    """
```

## 🔧 Development

### Adding New Pages
1. Create page file in `pages/` directory
2. Add navigation in `main.py`
3. Implement authentication if needed
4. Add API integration
5. Style with custom CSS

### Best Practices
- Use `st.session_state` for data persistence
- Implement proper error handling
- Add loading states for API calls
- Follow Streamlit naming conventions
- Use caching for expensive operations

### Performance Optimization
- Use `@st.cache_data` for data caching
- Minimize API calls with session storage
- Optimize chart rendering
- Implement pagination for large datasets

## 🤝 Contributing

1. Follow Streamlit best practices
2. Maintain consistent styling
3. Add proper error handling
4. Test all user interactions
5. Update documentation

## 📱 Mobile Responsiveness

The application is designed to work on various screen sizes:
- Responsive grid layouts
- Mobile-friendly navigation
- Touch-optimized interactions
- Scalable charts and components

## 🔍 Debugging

### Common Issues
- **API Connection**: Check backend URL and server status
- **Authentication**: Verify JWT token and session state
- **Page Navigation**: Ensure proper page switching
- **Data Loading**: Check API responses and error handling

### Debug Tools
- Streamlit's built-in debugging
- Browser developer tools
- Network tab for API monitoring
- Console logs for JavaScript errors

---

**For more information, see the main project README.md**
