import streamlit as st
from services.api import api_client

# Page configuration
st.set_page_config(
    page_title="Login - LMS",
    page_icon="🔑",
    layout="centered"
)

# Custom CSS
st.markdown("""
<style>
    .login-container {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
    }
    
    .login-header {
        text-align: center;
        color: #667eea;
        margin-bottom: 2rem;
    }
    
    .demo-credentials {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #17a2b8;
        margin-top: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="login-header">
        <h1>🔑 Login to LMS</h1>
        <p>Access your learning dashboard</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Check if already logged in
    if "jwt_token" in st.session_state:
        st.success("You are already logged in!")
        if st.button("Go to Dashboard"):
            st.switch_page("main.py")
        return
    
    # Login form
    with st.container():
        st.markdown('<div class="login-container">', unsafe_allow_html=True)
        
        with st.form("login_form"):
            st.markdown("### Enter your credentials")
            
            email = st.text_input(
                "Email Address",
                placeholder="Enter your email",
                help="Use your registered email address"
            )
            
            password = st.text_input(
                "Password",
                type="password",
                placeholder="Enter your password",
                help="Enter your account password"
            )
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                login_button = st.form_submit_button(
                    "🔑 Login",
                    use_container_width=True,
                    type="primary"
                )
            
            with col2:
                if st.form_submit_button("📝 Register", use_container_width=True):
                    st.switch_page("pages/register.py")
        
        # Handle login
        if login_button:
            if not email or not password:
                st.error("Please enter both email and password")
            else:
                try:
                    with st.spinner("Logging in..."):
                        response = api_client.login(email, password)
                    
                    # Store token and user info in session
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    
                    st.success(f"Welcome back, {response['user']['full_name']}!")
                    st.balloons()
                    
                    # Redirect based on role
                    user_role = response["user"]["role"]
                    if user_role == "admin":
                        st.switch_page("pages/admin_dashboard.py")
                    elif user_role == "professor":
                        st.switch_page("pages/professor_dashboard.py")
                    elif user_role == "student":
                        st.switch_page("pages/student_dashboard.py")
                    else:
                        st.switch_page("main.py")
                        
                except Exception as e:
                    st.error(f"Login failed: {str(e)}")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Demo credentials section
    with st.expander("🔧 Demo Credentials"):
        st.markdown("""
        <div class="demo-credentials">
            <h4>Test Accounts</h4>
            
            <strong>👑 Admin Account:</strong><br>
            Email: <EMAIL><br>
            Password: admin123<br><br>
            
            <strong>👨‍🏫 Professor Account:</strong><br>
            Email: <EMAIL><br>
            Password: prof123<br><br>
            
            <strong>👨‍🎓 Student Account:</strong><br>
            Email: <EMAIL><br>
            Password: student123<br><br>
            
            <em>Note: Create these accounts using the registration page if they don't exist.</em>
        </div>
        """, unsafe_allow_html=True)
        
        # Quick login buttons for demo
        st.markdown("#### Quick Demo Login")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("Login as Admin", use_container_width=True):
                try:
                    response = api_client.login("<EMAIL>", "admin123")
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    st.success("Logged in as Admin!")
                    st.switch_page("pages/admin_dashboard.py")
                except Exception as e:
                    st.error("Admin account not found. Please create it first.")
        
        with col2:
            if st.button("Login as Professor", use_container_width=True):
                try:
                    response = api_client.login("<EMAIL>", "prof123")
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    st.success("Logged in as Professor!")
                    st.switch_page("pages/professor_dashboard.py")
                except Exception as e:
                    st.error("Professor account not found. Please create it first.")
        
        with col3:
            if st.button("Login as Student", use_container_width=True):
                try:
                    response = api_client.login("<EMAIL>", "student123")
                    st.session_state.jwt_token = response["access_token"]
                    st.session_state.user = response["user"]
                    st.success("Logged in as Student!")
                    st.switch_page("pages/student_dashboard.py")
                except Exception as e:
                    st.error("Student account not found. Please create it first.")
    
    # Back to home
    st.markdown("---")
    if st.button("🏠 Back to Home", use_container_width=True):
        st.switch_page("main.py")

if __name__ == "__main__":
    main()
